"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for CapNhatDeNghiThanhToan model.
"""

from typing import Any, Dict, Optional  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401

from django_ledger.models import CapNhatDeNghiThanhToanModel  # noqa: F401,
from django_ledger.repositories.tien_mat.de_nghi_thanh_toan.cap_nhat_de_nghi_thanh_toan import (  # noqa: F401,
    CapNhatDeNghiThanhToanRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,

# Avoid circular import by importing inside methods when needed


class CapNhatDeNghiThanhToanService(BaseService):
    """
    Service class for handling CapNhatDeNghiThanhToan business logic.
    Implements the Service pattern for CapNhatDeNghiThanhToan.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the service.
        """
        self.repository = CapNhatDeNghiThanhToanRepository()
        self.chi_tiet_service = None  # Will be initialized lazily
        super().__init__()

    def _get_chi_tiet_service(self):  # noqa: C901
        """
        Lazy load chi_tiet_service to avoid circular import.  # noqa: E402
        """
        if self.chi_tiet_service is None:
            from django_ledger.services.tien_mat.de_nghi_thanh_toan.cap_nhat_de_nghi_thanh_toan.chi_tiet_cap_nhat_de_nghi_thanh_toan import (  # noqa: F401,
                ChiTietCapNhatDeNghiThanhToanService,
            )

            self.chi_tiet_service = ChiTietCapNhatDeNghiThanhToanService()
        return self.chi_tiet_service

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the queryset for CapNhatDeNghiThanhToanModel.

        Returns
        -------
        QuerySet
            The queryset
        """
        return self.repository.get_queryset()

    def list(  # noqa: C901
        self,
        entity_slug: str,
        search_query: str = None,
        status: str = None,
    ) -> QuerySet:
        """
        Get a list of CapNhatDeNghiThanhToan instances for a specific entity

        Parameters
        ----------
        entity_slug : str
            The slug of the entity
        search_query : str, optional
            Search query to filter results
        status : str, optional
            Status filter

        Returns
        -------
        QuerySet
            QuerySet of CapNhatDeNghiThanhToan instances
        """
        return self.repository.list(
            entity_slug=entity_slug,
            search_query=search_query,
            status=status,
        )

    def get(
        self, entity_slug: str, uuid: str
    ) -> Optional[CapNhatDeNghiThanhToanModel]:  # noqa: C901
        """
        Get a CapNhatDeNghiThanhToan instance by UUID

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the CapNhatDeNghiThanhToan instance

        Returns
        -------
        Optional[CapNhatDeNghiThanhToanModel]
            The CapNhatDeNghiThanhToan instance, or None if not found
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> CapNhatDeNghiThanhToanModel:  # noqa: C901
        """
        Create a new CapNhatDeNghiThanhToan instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new instance

        Returns
        -------
        CapNhatDeNghiThanhToanModel
            The created CapNhatDeNghiThanhToan instance
        """
        # Create the instance using repository
        return self.repository.create(entity_slug=entity_slug, data=data)

    @transaction.atomic
    def create_with_details(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> CapNhatDeNghiThanhToanModel:  # noqa: C901
        """
        Create a new CapNhatDeNghiThanhToan instance with chi_tiet details

        Parameters
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new instance including chi_tiet

        Returns
        -------
        CapNhatDeNghiThanhToanModel
            The created CapNhatDeNghiThanhToan instance
        """
        # Extract chi_tiet instances before creating the main instance
        chi_tiet_instances = data.pop("chi_tiet", [])
        # Create the main instance
        instance = self.create(entity_slug=entity_slug, data=data)
        # Create chi_tiet instances
        if chi_tiet_instances:
            chi_tiet_service = self._get_chi_tiet_service()
            chi_tiet_service.bulk_create(
                parent=instance, data=chi_tiet_instances
            )
        return instance

    def update(
        self, entity_slug: str, uuid: str, data: Dict[str, Any]
    ) -> CapNhatDeNghiThanhToanModel:  # noqa: C901
        """
        Update an existing CapNhatDeNghiThanhToan instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the instance to update
        data : Dict[str, Any]
            The data to update

        Returns
        -------
        CapNhatDeNghiThanhToanModel
            The updated CapNhatDeNghiThanhToan instance

        Raises
        ------
        CapNhatDeNghiThanhToanModel.DoesNotExist
            If the instance does not exist
        """
        return self.repository.update(
            entity_slug=entity_slug, uuid=uuid, data=data
        )

    def delete(self, entity_slug: str, uuid: str) -> None:  # noqa: C901
        """
        Delete a CapNhatDeNghiThanhToan instance

        Parameters
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the instance to delete

        Raises
        ------
        CapNhatDeNghiThanhToanModel.DoesNotExist
            If the instance does not exist
        """
        self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    def get_by_document_number(
        self, entity_slug: str, so_ct: str
    ) -> Optional[CapNhatDeNghiThanhToanModel]:  # noqa: C901
        """
        Get CapNhatDeNghiThanhToan by document number

        Parameters
        ----------
        entity_slug : str
            The entity slug
        so_ct : str
            The document number

        Returns
        -------
        Optional[CapNhatDeNghiThanhToanModel]
            The CapNhatDeNghiThanhToan instance, or None if not found
        """
        return self.repository.get_by_document_number(
            entity_slug=entity_slug, so_ct=so_ct
        )
