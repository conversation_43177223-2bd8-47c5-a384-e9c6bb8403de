"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Dịch vụ cho Sổ Chi Tiết Công Nợ Theo <PERSON> - Logic nghiệp vụ báo cáo công nợ.

Nâng cao với báo cáo chi tiết cấp độ giao dịch để phân tích công nợ khách hàng.
Cập nhật: 2024-12-19 - Triển khai ERP Chuyên nghiệp
"""
from typing import Any, Dict, List
from datetime import datetime, date, timedelta
from decimal import Decimal

from django.utils import timezone
from django_ledger.services.base import BaseService
from django_ledger.models import (
    CustomerModel,
    EntityModel,
    TransactionModel,
    JournalEntryModel,
    HoaDonBanHangModel,
    PhieuThuModel,
    PhieuChiModel
)
from django_ledger.utils_new.debt_management import DebtCalculationService
from django_ledger.utils_new.debt_management.debt_calculation import (
    DebtBalanceCalculationUtils,
    DebtBalanceFormatterUtils,
    DebtBalanceQueryUtils
)
from django_ledger.utils_new.debt_management.debt_detail_utils import DebtDetailUtils


class SoChiTietCongNoTheoKhachHangService(BaseService):
    """
    Lớp dịch vụ xử lý logic nghiệp vụ Sổ Chi Tiết Công Nợ Theo Khách Hàng.

    Báo cáo này hiển thị chi tiết giao dịch cá nhân cho tài khoản công nợ khách hàng,
    cung cấp lịch sử giao dịch từng dòng với số dư lũy kế.

    Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm
    """

    def __init__(self):
        """
        Khởi tạo dịch vụ với khả năng tính toán công nợ.
        """
        super().__init__()
        self.debt_service = None

    def generate_report(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Tạo báo cáo chi tiết công nợ khách hàng hiển thị từng giao dịch.

        Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm

        Báo cáo này cung cấp chi tiết cấp độ giao dịch cho tài khoản công nợ khách hàng:
        - Từng bút toán và giao dịch riêng lẻ
        - Tính toán số dư lũy kế
        - Tham chiếu và mô tả giao dịch
        - Tham chiếu chéo tài khoản và khách hàng

        Parameters
        ----------
        entity_slug : str
            Slug của đơn vị
        filters : Dict[str, Any]
            Tham số bộ lọc bao gồm kỳ, tài khoản, khách hàng, v.v.

        Returns
        -------
        List[Dict[str, Any]]
            Dữ liệu báo cáo với chi tiết cấp độ giao dịch
        """
        try:
            # Lấy thông tin đơn vị
            entity = EntityModel.objects.get(slug=entity_slug)

            # Khởi tạo dịch vụ công nợ cho đơn vị này (theo mẫu bang_can_doi_phat_sinh_cong_no)
            if self.debt_service is None:
                self.debt_service = DebtCalculationService(entity_id=str(entity.uuid))

            # Phân tích bộ lọc sử dụng parser nâng cao (theo mẫu bang_can_doi_phat_sinh_cong_no)
            parsed_filters = self._parse_enhanced_filters(filters)

            # Tạo các dòng tổng hợp trước (Đầu kỳ, Tổng phát sinh, Cuối kỳ)
            summary_rows = self._generate_summary_rows(entity, parsed_filters)

            # Lấy các giao dịch đã lọc cho các dòng chi tiết
            transactions = self._get_filtered_transactions(entity, parsed_filters)

            # Tạo dữ liệu báo cáo bắt đầu với các dòng tổng hợp
            report_data = summary_rows.copy()
            stt = len(summary_rows) + 1  # Tiếp tục đánh số sau các dòng tổng hợp

            for transaction in transactions:
                try:
                    # Định dạng dữ liệu giao dịch
                    transaction_data = self._format_transaction_record(
                        stt=stt,
                        transaction=transaction,
                        parsed_filters=parsed_filters
                    )

                    if transaction_data:
                        report_data.append(transaction_data)
                        stt += 1

                        # Nếu yêu cầu ct_vt, thêm chi tiết sản phẩm sau giao dịch
                        if parsed_filters.get('ct_vt', False):
                            # Lấy chứng từ nguồn từ sổ cái
                            ledger = transaction.journal_entry.ledger
                            source_document = DebtDetailUtils.get_source_document_from_ledger(ledger)

                            # Lấy chi tiết sản phẩm nếu đây là hóa đơn bán hàng
                            if source_document and hasattr(source_document, 'chi_tiet_hoa_don_ban_hang_set'):
                                product_details = self._get_invoice_details(source_document)

                                # Thêm mỗi chi tiết sản phẩm như một dòng riêng biệt
                                for product_detail in product_details:
                                    product_detail_record = self._format_product_detail_record(
                                        stt=stt,
                                        product_detail=product_detail,
                                        base_transaction_record=transaction_data,
                                        parsed_filters=parsed_filters
                                    )

                                    if product_detail_record:
                                        report_data.append(product_detail_record)
                                        stt += 1

                except Exception as e:
                    # Ghi log lỗi cho giao dịch cá nhân nhưng tiếp tục xử lý
                    continue

            # Tính số dư lũy kế nếu yêu cầu so_du
            if parsed_filters.get('so_du', False):
                self._calculate_running_balance(report_data, parsed_filters)

            return report_data

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating customer debt detail report: {str(e)}", exc_info=True)

            # Return empty list instead of raising exception
            return []



    def _parse_enhanced_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse and validate filters for enhanced debt calculation.
        Following the pattern from bang_can_doi_phat_sinh_cong_no service.

        Parameters
        ----------
        filters : Dict[str, Any]
            Raw filter parameters

        Returns
        -------
        Dict[str, Any]
            Parsed and validated filters
        """
        from datetime import timedelta

        parsed = {}

        # Parse date range
        start_date_str = filters.get('ngay_ct1', '20240101')
        end_date_str = filters.get('ngay_ct2', '20241231')

        try:
            parsed['start_date'] = datetime.strptime(start_date_str, '%Y%m%d').date()
            parsed['end_date'] = datetime.strptime(end_date_str, '%Y%m%d').date()
        except ValueError:
            # Default to current year
            current_year = datetime.now().year
            parsed['start_date'] = date(current_year, 1, 1)
            parsed['end_date'] = date(current_year, 12, 31)

        # Parse required UUIDs (always present)
        parsed['tk'] = filters.get('tk')  # Account UUID
        parsed['customer_uuids'] = [filters.get('ma_kh')]  # Customer UUID

        # Parse other filters
        parsed['ma_unit'] = filters.get('ma_unit', '')
        parsed['ma_bp'] = filters.get('ma_bp', '')
        parsed['ma_vv'] = filters.get('ma_vv', '')

        # Parse ct_vt flag for product detail display
        parsed['ct_vt'] = filters.get('ct_vt', False)
        if isinstance(parsed['ct_vt'], str):
            parsed['ct_vt'] = parsed['ct_vt'].lower() in ['true', '1', 'yes']

        # Parse so_du flag for running balance display (IntegerField: 0=no, 1=yes)
        parsed['so_du'] = filters.get('so_du', 0)
        # Convert to boolean for internal logic (1 = true, 0 = false)
        parsed['so_du'] = bool(parsed['so_du'])

        return parsed



    def _generate_summary_rows(self, entity: EntityModel, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate the first 3 summary rows: Đầu kỳ, Tổng phát sinh, Cuối kỳ.

        Uses debt_management utils to calculate accurate debt figures following ERP standards.

        Parameters
        ----------
        entity : EntityModel
            The entity to calculate debt for
        filters : Dict[str, Any]
            Parsed filter parameters

        Returns
        -------
        List[Dict[str, Any]]
            List of 3 summary rows with debt calculations
        """
        try:
            # Get customer and account info first
            customer_uuid = filters['customer_uuids'][0]
            account_uuid = filters['tk']
            customer = CustomerModel.objects.get(uuid=customer_uuid)

            # Calculate opening balance using unified utils
            from datetime import timedelta
            opening_date = filters['start_date'] - timedelta(days=1)
            opening_balance = DebtBalanceCalculationUtils.calculate_opening_balance(
                customer=customer,
                end_date=opening_date,
                account_uuids=[account_uuid],
                entity_id=str(entity.uuid)
            )

            # Calculate period totals using unified utils
            period_debit, period_credit = DebtBalanceCalculationUtils.calculate_period_movements(
                customer=customer,
                start_date=filters['start_date'],
                end_date=filters['end_date'],
                account_uuids=[account_uuid],
                entity_id=str(entity.uuid)
            )
            # Calculate closing balance using unified utils
            closing_balance = DebtBalanceCalculationUtils.calculate_closing_balance(
                opening_balance=opening_balance,
                period_debit=period_debit,
                period_credit=period_credit
            )

            # Format totals using unified utils (import already at top)
            total_opening_debit, total_opening_credit = DebtBalanceFormatterUtils.format_balance_columns(opening_balance)
            total_period_debit = float(period_debit)
            total_period_credit = float(period_credit)
            total_closing_debit, total_closing_credit = DebtBalanceFormatterUtils.format_balance_columns(closing_balance)

            # Create summary rows with calculated totals (following ERP standards)
            summary_rows = [
                # Row 1: Đầu kỳ - Opening balance totals
                DebtDetailUtils.create_summary_row(
                    stt=1,
                    dien_giai="Đầu kỳ",
                    ps_no=total_opening_debit,
                    ps_co=total_opening_credit
                ),
                # Row 2: Tổng phát sinh - Period movement totals
                DebtDetailUtils.create_summary_row(
                    stt=2,
                    dien_giai="Tổng phát sinh",
                    ps_no=total_period_debit,
                    ps_co=total_period_credit
                ),
                # Row 3: Cuối kỳ - Closing balance totals
                DebtDetailUtils.create_summary_row(
                    stt=3,
                    dien_giai="Cuối kỳ",
                    ps_no=total_closing_debit,
                    ps_co=total_closing_credit
                )
            ]

            # Add product detail columns to summary rows if ct_vt is requested
            if filters.get('ct_vt', False):
                for row in summary_rows:
                    row.update({
                        'so_luong': 0.0,
                        'gia2': 0.0,
                        'tien2': 0.0,
                    })

            # Add running balance columns to summary rows if so_du is requested
            if filters.get('so_du', False):
                for row in summary_rows:
                    row.update({
                        'du_no': 0.0,  # Empty for summary rows
                        'du_co': 0.0,  # Empty for summary rows
                    })

            return summary_rows

        except Exception as e:
            # Return empty summary rows on error
            return DebtDetailUtils.create_empty_summary_rows()

    def _get_filtered_transactions(self, entity: EntityModel, filters: Dict[str, Any]) -> List[TransactionModel]:
        """
        Get transactions based on filter criteria.

        Parameters
        ----------
        entity : EntityModel
            The entity to filter transactions for
        filters : Dict[str, Any]
            Parsed filter parameters

        Returns
        -------
        List[TransactionModel]
            Filtered transaction list
        """
        # Base queryset for customer-related transactions
        queryset = TransactionModel.objects.filter(
            journal_entry__ledger__entity=entity,
            journal_entry__customer__isnull=False,
            journal_entry__posted=True,
            journal_entry__timestamp__date__gte=filters['start_date'],
            journal_entry__timestamp__date__lte=filters['end_date'],
            amount__gt=0  # Only include transactions with amount > 0
        ).select_related(
            'journal_entry',
            'journal_entry__customer',
            'account'
        ).order_by('journal_entry__timestamp', 'journal_entry__uuid', 'uuid')

        # Filter by account UUID (always provided)
        queryset = queryset.filter(account__uuid=filters['tk'])

        # Filter by customer UUID (always provided)
        queryset = queryset.filter(
            journal_entry__customer__uuid__in=filters['customer_uuids']
        )

        return list(queryset)

    def _get_invoice_details(self, source_document) -> List[Dict[str, Any]]:
        """
        Get product details from invoice (hóa đơn bán hàng).

        Parameters
        ----------
        source_document : HoaDonBanHangModel
            The source invoice document

        Returns
        -------
        List[Dict[str, Any]]
            List of product detail records
        """
        if not source_document:
            return []

        # Check if this is a sales invoice with details
        if hasattr(source_document, 'chi_tiet_hoa_don_ban_hang_set'):
            try:
                details = source_document.chi_tiet_hoa_don_ban_hang_set.all().select_related(
                    'vat_tu'
                ).order_by('id')

                detail_records = []
                for detail in details:
                    # Format product detail record
                    detail_record = {
                        'ma_vt': detail.vat_tu.ma_vt if detail.vat_tu else '',
                        'ten_vt0': detail.vat_tu.ten_vt0 if detail.vat_tu else '',
                        'so_luong': float(detail.so_luong) if detail.so_luong else 0.0,
                        'gia2': float(detail.gia2) if detail.gia2 else 0.0,
                        'tien2': float(detail.tien2) if detail.tien2 else 0.0,
                    }
                    detail_records.append(detail_record)

                return detail_records

            except Exception:
                return []

        return []

    def _format_product_detail_record(
        self,
        stt: int,
        product_detail: Dict[str, Any],
        base_transaction_record: Dict[str, Any],
        parsed_filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Format a product detail record based on transaction record template.

        Parameters
        ----------
        stt : int
            Sequential number
        product_detail : Dict[str, Any]
            Product detail data from invoice
        base_transaction_record : Dict[str, Any]
            Base transaction record to copy fields from
        parsed_filters : Dict[str, Any]
            Parsed filter parameters

        Returns
        -------
        Dict[str, Any]
            Formatted product detail record
        """
        # Copy base transaction record and modify specific fields
        detail_record = base_transaction_record.copy()

        # Update specific fields for product detail
        detail_record.update({
            'stt': stt,
            'tk': '',  # Empty for product detail
            'tk_du': '',  # Empty for product detail
            'ps_no': 0.0,  # Empty for product detail
            'ps_co': 0.0,  # Empty for product detail
            'dien_giai': f"{product_detail['ma_vt']} - {product_detail['ten_vt0']}",
            'line': stt,
            # Add product detail columns
            'so_luong': product_detail['so_luong'],
            'gia2': product_detail['gia2'],
            'tien2': product_detail['tien2'],
        })

        return detail_record



    def _format_transaction_record(
        self, stt: int, transaction: TransactionModel, parsed_filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Format a single transaction record for the report.

        Expert ERP Implementation - 20+ years experience

        Logic:
        1. Từ transaction → journal_entry → ledger → tìm hóa đơn/phiếu
        2. Map data từ hóa đơn/phiếu vào response fields
        3. Tính tk_du từ giao dịch đối ứng trong cùng journal_entry

        Parameters
        ----------
        stt : int
            Sequential number
        transaction : TransactionModel
            The transaction to format
        parsed_filters : Dict[str, Any]
            Parsed filter parameters

        Returns
        -------
        Dict[str, Any]
            Formatted transaction record with data from invoice/receipt
        """
        journal_entry = transaction.journal_entry
        customer = journal_entry.customer
        ledger = journal_entry.ledger

        # Get source document (hóa đơn/phiếu) from ledger relationship
        source_document = DebtDetailUtils.get_source_document_from_ledger(ledger)

        # Calculate tk_du (corresponding account) from journal entry transactions
        tk_du, ps_no, ps_co = DebtDetailUtils.calculate_corresponding_account_and_amounts(
            transaction, journal_entry
        )

        # Map data from source document if available
        document_data = DebtDetailUtils.extract_document_data(source_document)

        # Base transaction record
        record = {
            'stt': stt,
            'tk': transaction.account.code if transaction.account else '',
            'so_ct0': document_data.get('so_ct0', ''),
            'ngay_ct0': document_data.get('ngay_ct0', ''),
            'xorder': stt,  # Order field
            'line': stt,  # Use stt as line number (integer)
            'id': str(transaction.uuid),
            'unit_id': document_data.get('unit_id', ''),
            'ma_ct': document_data.get('ma_ct', ''),
            'ngay_ct': document_data.get('ngay_ct', ''),
            'so_ct': document_data.get('so_ct', ''),
            'tk_du': tk_du,
            'ps_no': ps_no,
            'ps_co': ps_co,
            'dien_giai': document_data.get('dien_giai', journal_entry.description or transaction.description or ''),
            'ma_bp': parsed_filters.get('ma_bp', ''),
            'ma_vv': parsed_filters.get('ma_vv', ''),
            'ma_unit': parsed_filters.get('ma_unit', ''),
        }

        # Add product detail columns if ct_vt is requested
        if parsed_filters.get('ct_vt', False):
            record.update({
                'so_luong': 0.0,  # Default empty for transaction records
                'gia2': 0.0,      # Default empty for transaction records
                'tien2': 0.0,     # Default empty for transaction records
            })

        # Add running balance columns if so_du is requested
        if parsed_filters.get('so_du', False):
            record.update({
                'du_no': 0.0,  # Will be calculated in main loop
                'du_co': 0.0,  # Will be calculated in main loop
            })

        return record

    def _calculate_running_balance(self, report_data: List[Dict[str, Any]], parsed_filters: Dict[str, Any]) -> None:
        """
        Tính số dư lũy kế cho các bản ghi giao dịch khi so_du=true.

        Triển khai ERP Chuyên nghiệp - Tính toán Số dư Lũy kế

        Logic:
        1. Bắt đầu với số dư đầu kỳ từ dòng tổng hợp 1 (Đầu kỳ)
        2. Với mỗi giao dịch: số_dư_lũy_kế += ps_no - ps_co
        3. Hiển thị số dư dương trong du_no, số dư âm trong du_co

        Parameters
        ----------
        report_data : List[Dict[str, Any]]
            Dữ liệu báo cáo chứa các bản ghi tổng hợp và giao dịch
        parsed_filters : Dict[str, Any]
            Tham số bộ lọc đã phân tích
        """
        if len(report_data) < 3:
            return  # Cần ít nhất 3 dòng tổng hợp

        # Lấy số dư đầu kỳ từ dòng tổng hợp 1 (Đầu kỳ)
        opening_row = report_data[0]  # Dòng 1: Đầu kỳ
        opening_debit = float(opening_row.get('ps_no', 0.0))
        opening_credit = float(opening_row.get('ps_co', 0.0))

        # Tính số dư lũy kế ban đầu (số dư đầu kỳ)
        running_balance = opening_debit - opening_credit

        # Xử lý các bản ghi giao dịch (bắt đầu từ dòng 4, chỉ số 3)
        for i in range(3, len(report_data)):
            record = report_data[i]

            # Bỏ qua các dòng chi tiết sản phẩm (có trường tk trống)
            if not record.get('tk', '').strip():
                # Đối với dòng chi tiết sản phẩm, sao chép số dư từ giao dịch cha
                if i > 3:  # Đảm bảo có giao dịch trước đó
                    prev_record = report_data[i-1]
                    record['du_no'] = prev_record.get('du_no', 0.0)
                    record['du_co'] = prev_record.get('du_co', 0.0)
                continue

            # Đây là bản ghi giao dịch - tính số dư lũy kế
            ps_no = float(record.get('ps_no', 0.0))
            ps_co = float(record.get('ps_co', 0.0))

            # Cập nhật số dư lũy kế
            running_balance += ps_no - ps_co

            # Định dạng cột số dư dựa trên dương/âm
            if running_balance > 0:
                record['du_no'] = abs(running_balance)
                record['du_co'] = 0.0
            elif running_balance < 0:
                record['du_no'] = 0.0
                record['du_co'] = abs(running_balance)
            else:
                record['du_no'] = 0.0
                record['du_co'] = 0.0
