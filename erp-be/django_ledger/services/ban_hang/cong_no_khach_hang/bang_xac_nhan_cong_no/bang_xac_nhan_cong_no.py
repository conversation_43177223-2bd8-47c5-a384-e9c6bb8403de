"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Dịch vụ cho Bảng Xác Nhận Công Nợ - Logic nghiệp vụ báo cáo xác nhận công nợ khách hàng.

<PERSON><PERSON>o cáo này hiển thị chi tiết giao dịch để xác nhận số dư công nợ với khách hàng,
cung cấp thông tin chi tiết từng bút toán với tài khoản đối ứng và mô tả giao dịch.

Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm
Cập nhật: 2025-01-06 - Expert ERP Implementation
"""
from typing import Any, Dict, List
from datetime import datetime, date, timedelta
from decimal import Decimal

from django.utils import timezone
from django_ledger.services.base import BaseService
from django_ledger.models import (
    CustomerModel,
    EntityModel,
    TransactionModel,
    JournalEntryModel,
    HoaDonBanHangModel,
    PhieuThuModel,
    PhieuChiModel
)
from django_ledger.utils_new.debt_management import DebtCalculationService
from django_ledger.utils_new.debt_management.debt_calculation import (
    DebtBalanceCalculationUtils,
    DebtBalanceFormatterUtils,
    DebtBalanceQueryUtils
)
from django_ledger.utils_new.debt_management.debt_detail_utils import DebtDetailUtils


class BangXacNhanCongNoService(BaseService):
    """
    Lớp dịch vụ xử lý logic nghiệp vụ Bảng Xác Nhận Công Nợ.

    Báo cáo này hiển thị chi tiết giao dịch để xác nhận số dư công nợ với khách hàng:
    - Từng bút toán và giao dịch riêng lẻ với thông tin chi tiết
    - Tài khoản đối ứng (tk_du) cho mỗi giao dịch
    - Thông tin chứng từ gốc (hóa đơn, phiếu thu/chi)
    - Mô tả giao dịch và tham chiếu chéo

    Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm
    """

    def __init__(self):
        """
        Khởi tạo dịch vụ với khả năng tính toán công nợ.
        """
        super().__init__()
        self.debt_service = None

    def generate_report(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Tạo báo cáo xác nhận công nợ khách hàng hiển thị từng giao dịch chi tiết.

        Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm

        Báo cáo này cung cấp chi tiết cấp độ giao dịch để xác nhận công nợ:
        - Từng bút toán và giao dịch riêng lẻ
        - Tài khoản đối ứng và số tiền nợ/có
        - Thông tin chứng từ và mô tả giao dịch
        - Tham chiếu chéo tài khoản và khách hàng

        Parameters
        ----------
        entity_slug : str
            Slug của đơn vị
        filters : Dict[str, Any]
            Tham số bộ lọc bao gồm kỳ, tài khoản, khách hàng, v.v.

        Returns
        -------
        List[Dict[str, Any]]
            Dữ liệu báo cáo với chi tiết cấp độ giao dịch cho xác nhận công nợ
        """
        try:
            # Lấy thông tin đơn vị
            entity = EntityModel.objects.get(slug=entity_slug)

            # Khởi tạo dịch vụ công nợ cho đơn vị này
            if self.debt_service is None:
                self.debt_service = DebtCalculationService(entity_id=str(entity.uuid))

            # Phân tích bộ lọc sử dụng parser nâng cao
            parsed_filters = self._parse_enhanced_filters(filters)

            # Lấy các giao dịch đã lọc cho báo cáo xác nhận
            transactions = self._get_filtered_transactions(entity, parsed_filters)

            # Tạo dữ liệu báo cáo từ các giao dịch
            report_data = []
            stt = 1

            for transaction in transactions:
                try:
                    # Định dạng dữ liệu giao dịch cho xác nhận công nợ
                    transaction_data = self._format_debt_confirmation_record(
                        stt=stt,
                        transaction=transaction,
                        parsed_filters=parsed_filters
                    )

                    if transaction_data:
                        report_data.append(transaction_data)
                        stt += 1

                except Exception as e:
                    # Ghi log lỗi cho giao dịch cá nhân nhưng tiếp tục xử lý
                    continue

            # Tính số dư lũy kế nếu yêu cầu so_du
            if parsed_filters.get('so_du', False):
                self._calculate_running_balance(report_data, parsed_filters)

            return report_data

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating debt confirmation report: {str(e)}", exc_info=True)

            # Return empty list instead of raising exception
            return []

    def _parse_enhanced_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Phân tích và xác thực bộ lọc cho tính toán công nợ nâng cao.

        Parameters
        ----------
        filters : Dict[str, Any]
            Tham số bộ lọc thô

        Returns
        -------
        Dict[str, Any]
            Bộ lọc đã phân tích và xác thực
        """
        parsed = {}

        # Parse date range - từ cURL request
        start_date_str = filters.get('ngay_ct1', '20250101')
        end_date_str = filters.get('ngay_ct2', '20251231')

        try:
            parsed['start_date'] = datetime.strptime(start_date_str, '%Y%m%d').date()
            parsed['end_date'] = datetime.strptime(end_date_str, '%Y%m%d').date()
        except ValueError:
            # Default to current year
            current_year = datetime.now().year
            parsed['start_date'] = date(current_year, 1, 1)
            parsed['end_date'] = date(current_year, 12, 31)

        # Parse required UUIDs (always present in API requests)
        parsed['tk'] = filters.get('tk')  # Account UUID
        parsed['ma_kh'] = filters.get('ma_kh')  # Customer UUID

        # Parse additional date filters from cURL
        ngay_hd1_str = filters.get('ngay_hd1', '')
        ngay_hd2_str = filters.get('ngay_hd2', '')

        if ngay_hd1_str and ngay_hd2_str:
            try:
                parsed['ngay_hd1'] = datetime.strptime(ngay_hd1_str, '%Y%m%d').date()
                parsed['ngay_hd2'] = datetime.strptime(ngay_hd2_str, '%Y%m%d').date()
            except ValueError:
                parsed['ngay_hd1'] = None
                parsed['ngay_hd2'] = None

        # Parse other filters from cURL request
        parsed['so_du'] = filters.get('so_du', 0)  # Balance filter (IntegerField: 0=no, 1=yes)
        parsed['ct_yn'] = filters.get('ct_yn', 1)  # Document type filter
        parsed['ma_unit'] = filters.get('ma_unit', '')  # Unit filter
        parsed['mau_bc'] = filters.get('mau_bc', 20)  # Report template
        parsed['nguoi_lap'] = filters.get('nguoi_lap', '')  # Creator filter

        # Convert so_du to boolean for internal logic (1 = true, 0 = false)
        parsed['so_du'] = bool(parsed['so_du'])

        return parsed

    def _get_filtered_transactions(self, entity: EntityModel, filters: Dict[str, Any]) -> List[TransactionModel]:
        """
        Lấy các giao dịch dựa trên tiêu chí bộ lọc cho báo cáo xác nhận công nợ.

        Parameters
        ----------
        entity : EntityModel
            Đơn vị để lọc giao dịch
        filters : Dict[str, Any]
            Tham số bộ lọc đã phân tích

        Returns
        -------
        List[TransactionModel]
            Danh sách giao dịch đã lọc
        """
        # Base queryset cho giao dịch liên quan đến khách hàng
        queryset = TransactionModel.objects.filter(
            journal_entry__ledger__entity=entity,
            journal_entry__customer__isnull=False,
            journal_entry__posted=True,
            journal_entry__timestamp__date__gte=filters['start_date'],
            journal_entry__timestamp__date__lte=filters['end_date'],
            amount__gt=0  # Chỉ bao gồm giao dịch có số tiền > 0
        ).select_related(
            'journal_entry',
            'journal_entry__customer',
            'journal_entry__ledger',
            'account'
        ).order_by('journal_entry__timestamp', 'journal_entry__uuid', 'uuid')

        # Lọc theo account UUID (luôn được cung cấp)
        if filters.get('tk'):
            queryset = queryset.filter(account__uuid=filters['tk'])

        # Lọc theo customer UUID (luôn được cung cấp)
        if filters.get('ma_kh'):
            queryset = queryset.filter(journal_entry__customer__uuid=filters['ma_kh'])

        # Lọc theo ngày hóa đơn nếu có
        if filters.get('ngay_hd1') and filters.get('ngay_hd2'):
            queryset = queryset.filter(
                journal_entry__timestamp__date__gte=filters['ngay_hd1'],
                journal_entry__timestamp__date__lte=filters['ngay_hd2']
            )

        return list(queryset)

    def _format_debt_confirmation_record(
        self, stt: int, transaction: TransactionModel, parsed_filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Định dạng một bản ghi giao dịch cho báo cáo xác nhận công nợ.

        Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm

        Logic xác nhận công nợ:
        1. Từ transaction → journal_entry → ledger → tìm hóa đơn/phiếu
        2. Map data từ hóa đơn/phiếu vào response fields theo yêu cầu
        3. Tính tk_du từ giao dịch đối ứng trong cùng journal_entry
        4. Cung cấp thông tin chi tiết để xác nhận số dư công nợ

        Parameters
        ----------
        stt : int
            Số thứ tự
        transaction : TransactionModel
            Giao dịch cần định dạng
        parsed_filters : Dict[str, Any]
            Tham số bộ lọc đã phân tích

        Returns
        -------
        Dict[str, Any]
            Bản ghi giao dịch đã định dạng với dữ liệu từ hóa đơn/phiếu
        """
        journal_entry = transaction.journal_entry
        customer = journal_entry.customer
        ledger = journal_entry.ledger

        # Lấy chứng từ nguồn (hóa đơn/phiếu) từ mối quan hệ ledger
        source_document = DebtDetailUtils.get_source_document_from_ledger(ledger)

        # Tính tk_du (tài khoản đối ứng) từ các giao dịch journal entry
        tk_du, ps_no, ps_co = DebtDetailUtils.calculate_corresponding_account_and_amounts(
            transaction, journal_entry
        )

        # Map dữ liệu từ chứng từ nguồn nếu có
        document_data = DebtDetailUtils.extract_document_data(source_document)

        # Bản ghi giao dịch xác nhận công nợ theo response fields yêu cầu
        record = {
            'stt': stt,
            'tk': transaction.account.code if transaction.account else '',
            'id': str(transaction.uuid),
            'unit_id': document_data.get('unit_id', ''),
            'ma_ct': document_data.get('ma_ct', ''),
            'ngay_ct': document_data.get('ngay_ct', ''),
            'so_ct': document_data.get('so_ct', ''),
            'ma_kh': customer.ma_kh if customer else '',
            'tk_du': tk_du,
            'ps_no': ps_no,
            'ps_co': ps_co,
            'dien_giai': document_data.get('dien_giai', ''),
            'ma_bp': document_data.get('ma_bp', ''),
            'ma_vv': document_data.get('ma_vv', ''),
            'ma_unit': document_data.get('ma_unit', ''),
            'so_dh': document_data.get('so_dh', ''),
            'id_dh': document_data.get('id_dh', ''),
        }

        # Thêm running balance columns nếu so_du được yêu cầu
        if parsed_filters.get('so_du', False):
            record.update({
                'du_no': 0.0,  # Sẽ được tính toán trong _calculate_running_balance
                'du_co': 0.0,  # Sẽ được tính toán trong _calculate_running_balance
            })

        return record

    def _calculate_running_balance(self, report_data: List[Dict[str, Any]], parsed_filters: Dict[str, Any]) -> None:
        """
        Tính số dư lũy kế cho các bản ghi giao dịch khi so_du=true.

        Triển khai ERP Chuyên nghiệp - Tính toán Số dư Lũy kế cho Xác nhận Công nợ

        Logic:
        1. Bắt đầu với số dư đầu kỳ = 0 (vì đây là báo cáo xác nhận, không có số dư đầu kỳ)
        2. Với mỗi giao dịch: số_dư_lũy_kế += ps_no - ps_co
        3. Hiển thị số dư dương trong du_no, số dư âm trong du_co

        Parameters
        ----------
        report_data : List[Dict[str, Any]]
            Dữ liệu báo cáo chứa các bản ghi giao dịch
        parsed_filters : Dict[str, Any]
            Tham số bộ lọc đã phân tích
        """
        if not report_data:
            return

        # Khởi tạo số dư lũy kế = 0 (báo cáo xác nhận bắt đầu từ 0)
        running_balance = 0.0

        # Xử lý tất cả các bản ghi giao dịch
        for record in report_data:
            # Lấy số tiền nợ và có từ giao dịch
            ps_no = float(record.get('ps_no', 0.0))
            ps_co = float(record.get('ps_co', 0.0))

            # Cập nhật số dư lũy kế
            running_balance += ps_no - ps_co

            # Định dạng cột số dư dựa trên dương/âm
            if running_balance > 0:
                record['du_no'] = abs(running_balance)
                record['du_co'] = 0.0
            elif running_balance < 0:
                record['du_no'] = 0.0
                record['du_co'] = abs(running_balance)
            else:
                record['du_no'] = 0.0
                record['du_co'] = 0.0


