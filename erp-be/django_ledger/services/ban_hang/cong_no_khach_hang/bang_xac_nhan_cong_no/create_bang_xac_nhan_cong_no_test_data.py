#!/usr/bin/env python3
"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Test data creation script for Bang Xac Nhan Cong No (Debt Confirmation Report).

This script creates comprehensive test data including:
- Customers with debt confirmation scenarios
- Journal entries with customer transactions
- Account transactions for debt confirmation tracking
- Realistic transaction patterns for debt verification

Usage:
    python create_bang_xac_nhan_cong_no_test_data.py <entity_slug>
"""

import os
import sys
import django
from datetime import datetime, date, timedelta
from decimal import Decimal

# Add the project root to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
# Navigate up to erp-be directory
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))))
sys.path.insert(0, project_root)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_ledger.settings')
django.setup()

from django_ledger.models import (
    EntityModel, CustomerModel, AccountModel, JournalEntryModel, 
    TransactionModel, LedgerModel, ChartOfAccountModel
)


def create_test_data(entity_slug: str):
    """
    Create comprehensive test data for debt confirmation report.
    
    Parameters
    ----------
    entity_slug : str
        The entity slug to create test data for
    """
    try:
        # Get entity
        entity = EntityModel.objects.get(slug=entity_slug)
        print(f"✅ Found entity: {entity.name} ({entity_slug})")
        
        # Get or create chart of accounts
        coa, created = ChartOfAccountModel.objects.get_or_create(
            entity=entity,
            defaults={'name': f'Chart of Accounts for {entity.name}'}
        )
        
        # Create test accounts for debt confirmation
        accounts_data = [
            {'code': '131', 'name': 'Accounts Receivable - Customers', 'role': 'asset_ca_receivables'},
            {'code': '1311', 'name': 'Trade Receivables', 'role': 'asset_ca_receivables'},
            {'code': '1312', 'name': 'Other Receivables', 'role': 'asset_ca_receivables'},
            {'code': '111', 'name': 'Cash', 'role': 'asset_ca_cash'},
            {'code': '112', 'name': 'Bank Account', 'role': 'asset_ca_cash'},
            {'code': '334', 'name': 'Customer Advances', 'role': 'liability_cl_acc_payable'},
            {'code': '511', 'name': 'Sales Revenue', 'role': 'income_sales'},
        ]
        
        accounts = {}
        for acc_data in accounts_data:
            account, created = AccountModel.objects.get_or_create(
                coa=coa,
                code=acc_data['code'],
                defaults={
                    'name': acc_data['name'],
                    'role': acc_data['role'],
                    'balance_type': 'debit' if acc_data['role'].startswith('asset') else 'credit'
                }
            )
            accounts[acc_data['code']] = account
            if created:
                print(f"✅ Created account: {acc_data['code']} - {acc_data['name']}")
        
        # Create test customers for debt confirmation
        customers_data = [
            {'code': '*********', 'name': 'ABC Trading Company', 'group': 'VIP'},  # From cURL
            {'code': 'KH001', 'name': 'XYZ Manufacturing Ltd', 'group': 'Standard'},
            {'code': 'KH002', 'name': 'DEF Services Co', 'group': 'Standard'},
            {'code': 'KH003', 'name': 'GHI Retail Store', 'group': 'Regular'},
            {'code': 'KH004', 'name': 'JKL Construction', 'group': 'VIP'},
        ]
        
        customers = {}
        for cust_data in customers_data:
            customer, created = CustomerModel.objects.get_or_create(
                entity_model=entity,
                customer_code=cust_data['code'],
                defaults={
                    'customer_name': cust_data['name'],
                    'customer_group1': cust_data['group'],
                    'active': True
                }
            )
            customers[cust_data['code']] = customer
            if created:
                print(f"✅ Created customer: {cust_data['code']} - {cust_data['name']}")
        
        # Get or create ledger
        ledger, created = LedgerModel.objects.get_or_create(
            entity=entity,
            defaults={'name': f'General Ledger - {entity.name}'}
        )
        
        # Create debt confirmation transaction scenarios
        base_date = date(2025, 6, 1)  # Match cURL ngay_hd1
        transaction_scenarios = [
            # Customer ********* (from cURL) - Debt confirmation scenarios
            {
                'customer': '*********',
                'account': '131',
                'corresponding_account': '511',
                'date': base_date,
                'reference': 'INV-2025-001',
                'description': 'Sales Invoice - Product A for debt confirmation',
                'debit': Decimal('2500000.00'),
                'credit': Decimal('0.00')
            },
            {
                'customer': '*********',
                'account': '131',
                'corresponding_account': '111',
                'date': base_date + timedelta(days=2),
                'reference': 'PMT-2025-001',
                'description': 'Payment received - partial',
                'debit': Decimal('0.00'),
                'credit': Decimal('1000000.00')
            },
            {
                'customer': '*********',
                'account': '131',
                'corresponding_account': '511',
                'date': base_date + timedelta(days=3),
                'reference': 'INV-2025-002',
                'description': 'Sales Invoice - Product B for confirmation',
                'debit': Decimal('1800000.00'),
                'credit': Decimal('0.00')
            },
            
            # Additional customers for comprehensive debt confirmation testing
            {
                'customer': 'KH001',
                'account': '131',
                'corresponding_account': '511',
                'date': base_date + timedelta(days=1),
                'reference': 'INV-2025-003',
                'description': 'Manufacturing services invoice',
                'debit': Decimal('4200000.00'),
                'credit': Decimal('0.00')
            },
            {
                'customer': 'KH001',
                'account': '131',
                'corresponding_account': '112',
                'date': base_date + timedelta(days=4),
                'reference': 'PMT-2025-002',
                'description': 'Bank transfer payment',
                'debit': Decimal('0.00'),
                'credit': Decimal('2000000.00')
            },
            
            {
                'customer': 'KH002',
                'account': '1311',
                'corresponding_account': '511',
                'date': base_date + timedelta(days=2),
                'reference': 'INV-2025-004',
                'description': 'Service contract - monthly',
                'debit': Decimal('1500000.00'),
                'credit': Decimal('0.00')
            },
        ]
        
        # Create journal entries and transactions for debt confirmation
        for i, scenario in enumerate(transaction_scenarios):
            # Create journal entry
            journal_entry = JournalEntryModel.objects.create(
                ledger=ledger,
                date=scenario['date'],
                reference=scenario['reference'],
                description=scenario['description'],
                customer=customers[scenario['customer']],
                posted=True
            )
            
            # Create main transaction (debit or credit)
            if scenario['debit'] > 0:
                TransactionModel.objects.create(
                    journal_entry=journal_entry,
                    account=accounts[scenario['account']],
                    tx_type='debit',
                    amount=scenario['debit'],
                    description=scenario['description']
                )
                # Create corresponding credit transaction
                TransactionModel.objects.create(
                    journal_entry=journal_entry,
                    account=accounts[scenario['corresponding_account']],
                    tx_type='credit',
                    amount=scenario['debit'],
                    description=f"Corresponding to {scenario['description']}"
                )
            
            if scenario['credit'] > 0:
                TransactionModel.objects.create(
                    journal_entry=journal_entry,
                    account=accounts[scenario['account']],
                    tx_type='credit',
                    amount=scenario['credit'],
                    description=scenario['description']
                )
                # Create corresponding debit transaction
                TransactionModel.objects.create(
                    journal_entry=journal_entry,
                    account=accounts[scenario['corresponding_account']],
                    tx_type='debit',
                    amount=scenario['credit'],
                    description=f"Corresponding to {scenario['description']}"
                )
            
            print(f"✅ Created debt confirmation transaction: {scenario['reference']} - {scenario['customer']}")
        
        print(f"\n🎉 Successfully created debt confirmation test data for entity: {entity_slug}")
        print(f"📊 Created {len(customers)} customers")
        print(f"📊 Created {len(accounts)} accounts")
        print(f"📊 Created {len(transaction_scenarios)} debt confirmation transactions")
        print(f"\n🔗 API Endpoint: /api/entities/{entity_slug}/erp/ban-hang/cong-no-khach-hang/bang-xac-nhan-cong-no/")
        
        # Print sample cURL command for testing
        print(f"\n📋 Sample cURL command for testing:")
        print(f"""curl -X POST 'http://localhost:8003/api/entities/{entity_slug}/erp/ban-hang/cong-no-khach-hang/bang-xac-nhan-cong-no/' \\
  -u 'admin:password' \\
  -H 'Content-Type: application/json' \\
  -d '{{
    "ngay_ct1": "********",
    "ngay_ct2": "********", 
    "tk": "{accounts['131'].uuid}",
    "ngay_hd1": "********",
    "ngay_hd2": "********",
    "ma_kh": "{customers['*********'].uuid}",
    "so_du": 0,
    "ct_yn": 1,
    "ma_unit": "",
    "mau_bc": 20,
    "nguoi_lap": ""
  }}'""")
        
    except EntityModel.DoesNotExist:
        print(f"❌ Entity with slug '{entity_slug}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error creating test data: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python create_bang_xac_nhan_cong_no_test_data.py <entity_slug>")
        sys.exit(1)
    
    entity_slug = sys.argv[1]
    create_test_data(entity_slug)
