"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for QuyenChungTu (Document Permission) model business logic
"""

from datetime import date, datetime  # noqa: F401
from typing import Any, Dict, List, Optional, Tuple, Union  # noqa: F401

from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models.entity import EntityModel  # noqa: F401,
from django_ledger.models.quyen_chung_tu import (  # noqa: F401,
    Quy<PERSON><PERSON>hungTu,
    <PERSON>uy<PERSON><PERSON>hungTuChiTiet,
    QuyenChungTuNgay,
)
from django_ledger.repositories.quyen_chung_tu.chi_tiet_repository import (  # noqa: F401,
    QuyenChungTuChiTietRepository,
)
from django_ledger.repositories.quyen_chung_tu.ngay_repository import (  # noqa: F401,
    QuyenChungTuNgayRepository,
)
from django_ledger.repositories.quyen_chung_tu.repository import (  # noqa: F401,
    QuyenChungTuRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,


class QuyenChungTuModelService(BaseService):
    """
    Service class for handling QuyenChungTu (Document Permission) model business logic
    """

    def __init__(self, entity_slug: str, user_model):  # noqa: C901
        self.entity_slug = entity_slug
        self.user_model = user_model
        self.repository = QuyenChungTuRepository()
        self.chi_tiet_repository = QuyenChungTuChiTietRepository()
        self.ngay_repository = QuyenChungTuNgayRepository()
        super().__init__()

    def _get_entity_model(self, entity_slug: str) -> EntityModel:  # noqa: C901
        """
        Helper method to get entity model from slug
        """
        return get_object_or_404(EntityModel, slug=entity_slug)

    def validate_quyen_chung_tu_data(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Validate document permission data

        Parameters
        ----------
        data : Dict[str, Any]
            The data to validate

        Returns
        -------
        Dict[str, Any]
            The validated data

        Raises
        ------
        ValueError
            If validation fails
        """
        validated_data = data.copy()
        # Validate required fields
        required_fields = [
            "ma_nk",
            "ten_nk",
            "so_ct_mau",
            "number_text",
            "ngay_hl1",
            "ngay_hl2",
        ]
        for field in required_fields:
            if field not in validated_data or not validated_data[field]:
                raise ValueError(f"Field '{field}' is required")
        # Validate date fields
        if "ngay_hl1" in validated_data and "ngay_hl2" in validated_data:
            ngay_hl1 = validated_data["ngay_hl1"]
            ngay_hl2 = validated_data["ngay_hl2"]
            # Convert string dates to date objects if needed
            if isinstance(ngay_hl1, str):
                try:
                    ngay_hl1 = datetime.strptime(ngay_hl1, "%Y-%m-%d").date()
                    validated_data["ngay_hl1"] = ngay_hl1
                except ValueError:
                    raise ValueError(
                        "Invalid date format for 'ngay_hl1'. Use YYYY-MM-DD format."
                    )

            if isinstance(ngay_hl2, str):
                try:
                    ngay_hl2 = datetime.strptime(ngay_hl2, "%Y-%m-%d").date()
                    validated_data["ngay_hl2"] = ngay_hl2
                except ValueError:
                    raise ValueError(
                        "Invalid date format for 'ngay_hl2'. Use YYYY-MM-DD format."
                    )

            # Ensure end date is after start date
            if ngay_hl1 > ngay_hl2:
                raise ValueError("End date must be after start date")

        # Validate unit_id if provided
        if "unit_id" in validated_data and validated_data["unit_id"]:
            from django_ledger.models import EntityUnitModel  # noqa: F401,

            # If unit_id is already an EntityUnitModel instance, no need to validate
            if isinstance(validated_data["unit_id"], EntityUnitModel):
                pass
            else:
                try:
                    # Try to get the unit by UUID
                    unit_id = validated_data["unit_id"]
                    EntityUnitModel.objects.get(uuid=unit_id)
                    # No need to convert here, as it will be done in the repository layer  # noqa: E501
                except (EntityUnitModel.DoesNotExist, ValueError, TypeError):
                    raise ValueError(
                        f"Invalid unit_id: {validated_data['unit_id']}"
                    )

        return validated_data

    def get_all_quyen_chung_tu(
        self, entity_slug: str, page: int = 1, page_size: int = 20
    ) -> Tuple[QuerySet, Dict]:  # noqa: C901
        """
        Get all document permissions for a specific entity

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        page : int, optional
            The page number, by default 1
        page_size : int, optional
            The number of items per page, by default 20

        Returns
        -------
        Tuple[QuerySet, Dict]
            A tuple containing the queryset of document permissions and pagination information  # noqa: E501
        """
        entity_model = self._get_entity_model(entity_slug)
        return self.repository.get_quyen_chung_tu_for_entity(
            entity_model=entity_model, page=page, page_size=page_size
        )

    def get_active_quyen_chung_tu(
        self, entity_slug: str, page: int = 1, page_size: int = 20
    ) -> Tuple[QuerySet, Dict]:  # noqa: C901
        """
        Get active document permissions for a specific entity

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        page : int, optional
            The page number, by default 1
        page_size : int, optional
            The number of items per page, by default 20

        Returns
        -------
        Tuple[QuerySet, Dict]
            A tuple containing the queryset of active document permissions and pagination information  # noqa: E501
        """
        entity_model = self._get_entity_model(entity_slug)
        return self.repository.get_active_quyen_chung_tu(
            entity_model=entity_model, page=page, page_size=page_size
        )

    def get_quyen_chung_tu(
        self, entity_slug: str, uuid: str
    ) -> Optional[QuyenChungTu]:  # noqa: C901
        """
        Get a document permission by UUID for a specific entity

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the document permission to retrieve

        Returns
        -------
        Optional[QuyenChungTu]
            The document permission model if found, None otherwise
        """
        quyen_chung_tu = self.repository.get_quyen_chung_tu_by_uuid(uuid)
        if (
            not quyen_chung_tu
            or quyen_chung_tu.entity_model.slug != entity_slug
        ):
            return None
        return quyen_chung_tu

    def get_quyen_chung_tu_by_ma_nk(
        self, entity_slug: str, ma_nk: str
    ) -> Optional[QuyenChungTu]:  # noqa: C901
        """
        Get a document permission by ma_nk for a specific entity

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        ma_nk : str
            The document permission code

        Returns
        -------
        Optional[QuyenChungTu]
            The document permission model if found, None otherwise
        """
        entity_model = self._get_entity_model(entity_slug)
        quyen_chung_tu = self.repository.get_quyen_chung_tu_by_ma_nk(ma_nk)
        if not quyen_chung_tu or quyen_chung_tu.entity_model != entity_model:
            return None
        return quyen_chung_tu

    def get_quyen_chung_tu_by_ma_ct(  # noqa: C901
        self,
        entity_slug: str,
        ma_ct: str,
        page: int = 1,
        page_size: int = 20,
    ) -> Tuple[QuerySet, Dict]:
        """
        Get document permissions for a specific document type

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        ma_ct : str
            The document type code
        page : int, optional
            The page number, by default 1
        page_size : int, optional
            The number of items per page, by default 20

        Returns
        -------
        Tuple[QuerySet, Dict]
            A tuple containing the queryset of document permissions and pagination information  # noqa: E501
        """
        entity_model = self._get_entity_model(entity_slug)
        return self.repository.get_quyen_chung_tu_by_ma_ct(
            entity_model=entity_model,
            ma_ct=ma_ct,
            page=page,
            page_size=page_size,
        )

    def get_quyen_chung_tu_by_chung_tu(  # noqa: C901
        self,
        entity_slug: str,
        ma_ct: str = None,
        chung_tu_uuid: str = None,
        ngay_hl: str = None,
    ) -> QuerySet:
        """
        Get document permissions that have chi tiet pointing to a specific chung tu
        with flexible filtering options

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        ma_ct : str, optional
            The document type code (ma_ct)
        chung_tu_uuid : str, optional
            The UUID of the chung tu (document type)
        ngay_hl : str, optional
            The effective date filter (ngay_hl)

        Returns
        -------
        QuerySet
            A queryset of document permissions that match the specified filters
        """
        entity_model = self._get_entity_model(entity_slug)
        queryset = self.repository.get_quyen_chung_tu_by_chung_tu_filters(
            ma_ct=ma_ct,
            chung_tu_uuid=chung_tu_uuid,
            ngay_hl=ngay_hl
        )
        # Filter by entity to ensure user only sees their entity's data
        return queryset.filter(entity_model=entity_model)

    def create_quyen_chung_tu(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> QuyenChungTu:  # noqa: C901
        """
        Create a new document permission for a specific entity

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        data : Dict[str, Any]
            The data for the new document permission

        Returns
        -------
        QuyenChungTu
            The created document permission model

        Raises
        ------
        ValueError
            If validation fails
        """
        # Get entity model
        entity_model = self._get_entity_model(entity_slug)
        # Extract danh_sach_chung_tu from data if present
        danh_sach_chung_tu = data.pop("danh_sach_chung_tu", None)
        # Validate data
        validated_data = self.validate_quyen_chung_tu_data(data)
        # Create document permission with entity_model
        quyen_chung_tu = self.repository.create_quyen_chung_tu(
            validated_data, entity_model=entity_model
        )

        # If danh_sach_chung_tu is provided, add each document type to the permission
        if danh_sach_chung_tu and isinstance(danh_sach_chung_tu, list):
            from django_ledger.models.chung_tu import (  # noqa: F401,
                ChungTu,
            )

            # First, validate all document types exist
            for ma_ct in danh_sach_chung_tu:
                try:
                    ChungTu.objects.get(ma_ct=ma_ct)
                except ChungTu.DoesNotExist:
                    raise ValueError(
                        f"Document type with code '{ma_ct}' not found"
                    )

            # Then add each document type
            for index, ma_ct in enumerate(danh_sach_chung_tu):
                try:
                    # Get user profile for the current user
                    user_profile = None
                    username = None
                    if self.user_model:
                        try:
                            from django_ledger.models.user_profile import (  # noqa: F401,
                                UserProfileModel,
                            )

                            user_profile = UserProfileModel.objects.get(
                                user=self.user_model()
                            )
                            username = self.user_model.username
                        except UserProfileModel.DoesNotExist:
                            pass

                    self.add_document_type(
                        entity_slug=entity_slug,
                        quyen_chung_tu_uuid=str(quyen_chung_tu.uuid),
                        data={
                            "ma_ct": ma_ct,
                            "line": index + 1,
                            "user_id": user_profile,
                            "username": username,
                        },
                    )
                except ValueError as e:
                    # Log error but continue with other document types
                    print(f"Error adding document type {ma_ct}: {str(e)}")

        return quyen_chung_tu

    def update_quyen_chung_tu(
        self, entity_slug: str, uuid: str, data: Dict[str, Any]
    ) -> Optional[QuyenChungTu]:  # noqa: C901
        """
        Update an existing document permission for a specific entity

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the document permission to update
        data : Dict[str, Any]
            The data to update the document permission with Returns
        -------
        Optional[QuyenChungTu]
            The updated document permission model, or None if not found

        Raises
        ------
        ValueError
            If validation fails
        """
        # Get entity model
        entity_model = self._get_entity_model(entity_slug)
        # Extract danh_sach_chung_tu from data if present
        danh_sach_chung_tu = data.pop("danh_sach_chung_tu", None)
        # Validate data
        validated_data = self.validate_quyen_chung_tu_data(data)
        # Check if document permission exists and belongs to entity
        quyen_chung_tu = self.get_quyen_chung_tu(entity_slug, uuid)
        if not quyen_chung_tu:
            raise ValueError("Document permission not found")

        # Update document permission with entity_model
        quyen_chung_tu = self.repository.update_quyen_chung_tu(
            uuid, validated_data, entity_model=entity_model
        )

        # If danh_sach_chung_tu is provided, update the document types
        if danh_sach_chung_tu is not None and isinstance(
            danh_sach_chung_tu, list
        ):
            from django_ledger.models.chung_tu import (  # noqa: F401,
                ChungTu,
            )

            # First, validate all document types exist
            for ma_ct in danh_sach_chung_tu:
                try:
                    ChungTu.objects.get(ma_ct=ma_ct)
                except ChungTu.DoesNotExist:
                    raise ValueError(
                        f"Document type with code '{ma_ct}' not found"
                    )

            # Get the chi_tiet repository to handle document types
            from django_ledger.repositories.quyen_chung_tu.chi_tiet_repository import (  # noqa: F401,
                QuyenChungTuChiTietRepository,
            )

            chi_tiet_repository = QuyenChungTuChiTietRepository()
            # First, remove all existing document types
            existing_chi_tiet = chi_tiet_repository.get_chi_tiet().filter(
                quyen_chung_tu=quyen_chung_tu
            )
            for chi_tiet in existing_chi_tiet:
                chi_tiet_repository.delete_chi_tiet(chi_tiet.uuid)

            # Then add the new document types
            for index, ma_ct in enumerate(danh_sach_chung_tu):
                try:
                    # Get user profile for the current user
                    user_profile = None
                    username = None
                    if self.user_model:
                        try:
                            from django_ledger.models.user_profile import (  # noqa: F401,
                                UserProfileModel,
                            )

                            user_profile = UserProfileModel.objects.get(
                                user=self.user_model()
                            )
                            username = self.user_model.username
                        except UserProfileModel.DoesNotExist:
                            pass

                    self.add_document_type(
                        entity_slug=entity_slug,
                        quyen_chung_tu_uuid=str(quyen_chung_tu.uuid),
                        data={
                            "ma_ct": ma_ct,
                            "line": index + 1,
                            "user_id": user_profile,
                            "username": username,
                        },
                    )
                except ValueError as e:
                    # Log error but continue with other document types
                    print(f"Error adding document type {ma_ct}: {str(e)}")

        return quyen_chung_tu

    def delete_quyen_chung_tu(
        self, entity_slug: str, uuid: str
    ) -> bool:  # noqa: C901
        """
        Delete a document permission for a specific entity

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        uuid : str
            The UUID of the document permission to delete

        Returns
        -------
        bool
            True if deletion was successful, False otherwise
        """
        # Check if document permission exists and belongs to entity
        quyen_chung_tu = self.get_quyen_chung_tu(entity_slug, uuid)
        if not quyen_chung_tu:
            return False

        # Delete document permission
        return self.repository.delete_quyen_chung_tu(uuid)

    def add_document_type(  # noqa: C901
        self, entity_slug: str, quyen_chung_tu_uuid: str, data: Dict[str, Any]
    ) -> Optional[QuyenChungTuChiTiet]:
        """
        Add a document type to a document permission

        Parameters
        ----------
        entity_slug : str
            The entity slug
        quyen_chung_tu_uuid : str
            The UUID of the document permission
        data : Dict[str, Any]
            The data for the new document type

        Returns
        -------
        Optional[QuyenChungTuChiTiet]
            The created document permission detail model, or None if not found

        Raises
        ------
        ValueError
            If validation fails
        """
        # Check if document permission exists and belongs to entity
        quyen_chung_tu = self.get_quyen_chung_tu(
            entity_slug, quyen_chung_tu_uuid
        )
        if not quyen_chung_tu:
            raise ValueError("Document permission not found")

        # Validate required fields
        if "ma_ct" not in data or not data["ma_ct"]:
            raise ValueError("Field 'ma_ct' is required")
        # Convert ma_ct from string to ChungTu instance if needed
        if isinstance(data["ma_ct"], str):
            from django_ledger.models.chung_tu import (  # noqa: F401,
                ChungTu,
            )

            try:
                chung_tu = ChungTu.objects.get(ma_ct=data["ma_ct"])
                data["ma_ct"] = chung_tu
            except ChungTu.DoesNotExist:
                raise ValueError(
                    f"Document type with code '{data['ma_ct']}' not found"
                )

        # Add quyen_chung_tu to data
        data["quyen_chung_tu"] = quyen_chung_tu
        data["ma_nk"] = quyen_chung_tu.ma_nk
        # Create document permission detail
        return self.chi_tiet_repository.create_chi_tiet(data)

    def track_document_number(  # noqa: C901
        self, entity_slug: str, quyen_chung_tu_uuid: str, data: Dict[str, Any]
    ) -> Optional[QuyenChungTuNgay]:
        """
        Track document number for a specific date

        Parameters:
        ----------
        entity_slug : str
            The entity slug
        quyen_chung_tu_uuid : str
            The UUID of the document permission
        data : Dict[str, Any]
            The data for the document number tracking

        Returns
        -------
        Optional[QuyenChungTuNgay]
            The created or updated document permission date model, or None if not found

        Raises
        ------
        ValueError
            If validation fails
        """
        # Check if document permission exists and belongs to entity
        quyen_chung_tu = self.get_quyen_chung_tu(
            entity_slug, quyen_chung_tu_uuid
        )
        if not quyen_chung_tu:
            raise ValueError("Document permission not found")

        # Validate required fields
        if "ngay_nk" not in data or not data["ngay_nk"]:
            raise ValueError("Field 'ngay_nk' is required")
        # Convert string date to date object if needed
        if isinstance(data["ngay_nk"], str):
            try:
                data["ngay_nk"] = datetime.strptime(
                    data["ngay_nk"], "%Y-%m-%d"
                ).date()
            except ValueError as e:
                raise ValueError(
                    "Invalid date format for 'ngay_nk'. Use YYYY-MM-DD format."
                )

        # Add quyen_chung_tu to data
        data["quyen_chung_tu"] = quyen_chung_tu
        data["ma_nk"] = quyen_chung_tu.ma_nk
        # If i_so_ct_ht is not provided, use the one from quyen_chung_tu
        if "i_so_ct_ht" not in data or data["i_so_ct_ht"] is None:
            data["i_so_ct_ht"] = quyen_chung_tu.i_so_ct_ht
        # Check if record already exists
        try:
            ngay_record = QuyenChungTuNgay.objects.get(
                quyen_chung_tu=quyen_chung_tu, ngay_nk=data["ngay_nk"]
            )
            # Update existing record
            return self.ngay_repository.update_ngay(ngay_record.uuid, data)
        except QuyenChungTuNgay.DoesNotExist:
            # Create new record
            return self.ngay_repository.create_ngay(data)
