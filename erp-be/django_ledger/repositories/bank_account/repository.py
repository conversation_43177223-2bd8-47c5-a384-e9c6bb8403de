"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for BankAccount model database operations
"""

from typing import Any, Dict, Optional, Tuple  # noqa: F401

from django.core.paginator import Paginator  # noqa: F401
from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models import (  # noqa: F401,
    AccountModel,
    BankAccountModel,
    EntityModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class BankAccountRepository(BaseRepository):
    """
    Repository for handling BankAccount model database operations
    """

    def __init__(self):  # noqa: C901
        self.model = BankAccountModel

    def get_bank_account_by_uuid(
        self, uuid: str
    ) -> Optional[BankAccountModel]:  # noqa: C901
        """
        Get a bank account by UUID
        """
        try:
            return self.model.objects.get(uuid=uuid)
        except self.model.DoesNotExist:
            return None

    def get_bank_accounts(self, **kwargs) -> QuerySet:  # noqa: C901
        """
        Get all bank accounts with optional filters
        """
        queryset = self.model.objects.all()
        if kwargs:
            queryset = queryset.filter(**kwargs)
        return queryset

    def get_bank_accounts_for_entity(  # noqa: C901
        self,
        entity_model: EntityModel,
        page: int = 1,
        page_size: int = 20,
    ) -> Tuple[QuerySet, Dict]:
        """
        Get all bank accounts for a specific entity with pagination
        """
        queryset = self.model.objects.filter(entity_model=entity_model)
        # Apply pagination
        paginator = Paginator(queryset, page_size)
        try:
            paginated_queryset = paginator.page(page)
        except Exception:
            paginated_queryset = paginator.page(1)
        pagination_info = {
            'count': paginator.count,
            'next': paginated_queryset.has_next() and page + 1 or None,
            'previous': paginated_queryset.has_previous() and page - 1 or None,
        }

        return paginated_queryset.object_list, pagination_info

    def create_bank_account(
        self, data: Dict[str, Any], entity_model=None
    ) -> BankAccountModel:  # noqa: C901
        """
        Create a new bank account

        Parameters
        ----------
        data : Dict[str, Any]
            The data for the new bank account
        entity_model : EntityModel, optional
            The entity model to associate with the bank account. If provided, it will override  # noqa: E501
            any entity_model in the data.

        Returns
        -------
        BankAccountModel
            The created bank account model
        """
        # Convert UUID strings to model instances for foreign key fields
        processed_data = self._convert_uuids_to_model_instances(data)
        # If entity_model is provided, use it
        if entity_model:
            processed_data['entity_model'] = entity_model
        return self.model.objects.create(**processed_data)

    def update_bank_account(
        self, uuid: str, data: Dict[str, Any], entity_model=None
    ) -> Optional[BankAccountModel]:  # noqa: C901
        """
        Update an existing bank account

        Parameters
        ----------
        uuid : str
            The UUID of the bank account to update
        data : Dict[str, Any]
            The data to update the bank account with
        entity_model : EntityModel, optional
            The entity model to associate with the bank account. If provided, it will override  # noqa: E501
            any entity_model in the data.

        Returns
        -------
        BankAccountModel
            The updated bank account model
        """
        bank_account = self.get_bank_account_by_uuid(uuid)
        if not bank_account:
            return None

        # Convert UUID strings to model instances for foreign key fields
        processed_data = self._convert_uuids_to_model_instances(data)
        # If entity_model is provided, use it
        if entity_model:
            processed_data['entity_model'] = entity_model
        # Update fields
        for key, value in processed_data.items():
            setattr(bank_account, key, value)

        bank_account.save()
        return bank_account

    def delete_bank_account(self, uuid: str) -> bool:  # noqa: C901
        """
        Delete a bank account by UUID

        Parameters
        ----------
        uuid : str
            The UUID of the bank account to delete

        Returns
        -------
        bool
            True if the bank account was deleted, False otherwise
        """
        bank_account = self.get_bank_account_by_uuid(uuid)
        if not bank_account:
            return False

        bank_account.delete()
        return True

    def _convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        from django_ledger.models import (  # noqa: F401,
            AccountModel,
            EntityUnitModel,
            NganHangModel,
        )

        processed_data = data.copy()
        # Convert account_model UUID to AccountModel instance
        if (
            'account_model' in processed_data
            and processed_data['account_model']
            and isinstance(processed_data['account_model'], str)
        ):
            try:
                processed_data['account_model'] = AccountModel.objects.get(
                    uuid=processed_data['account_model']
                )
            except AccountModel.DoesNotExist:
                raise ValueError(
                    f"Account with UUID {processed_data['account_model']} does not exist"
                )  # noqa: E501

        # Convert ma_ngan_hang UUID to NganHangModel instance
        if (
            'ma_ngan_hang' in processed_data
            and processed_data['ma_ngan_hang']
            and isinstance(processed_data['ma_ngan_hang'], str)
        ):
            try:
                processed_data['ma_ngan_hang'] = NganHangModel.objects.get(
                    uuid=processed_data['ma_ngan_hang']
                )
            except NganHangModel.DoesNotExist:
                raise ValueError(
                    f"NganHangModel with UUID {processed_data['ma_ngan_hang']} does not exist"
                )  # noqa: E501

        # Convert unit_id UUID to EntityUnitModel instance
        if (
            'unit_id' in processed_data
            and processed_data['unit_id']
            and isinstance(processed_data['unit_id'], str)
        ):
            try:
                processed_data['unit_id'] = EntityUnitModel.objects.get(
                    uuid=processed_data['unit_id']
                )
            except EntityUnitModel.DoesNotExist:
                raise ValueError(
                    f"EntityUnitModel with UUID {processed_data['unit_id']} does not exist"
                )  # noqa: E501

        return processed_data
