"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for ChiTietHoaDon model.
"""

from django.db.models import QuerySet
from django.shortcuts import get_object_or_404

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (
    ChiTietHoaDonModel,
    HoaDonDichVuModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: E402


class ChiTietHoaDonRepository(BaseRepository):
    """
    Repository class for ChiTietHoaDon model.
    """

    def __init__(self, model_class=None):  # noqa: F811,
        super().__init__(model_class or ChiTietHoaDonModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for ChiTietHoaDonModel with optimized select_related.

        Returns
        -------
        QuerySet
            Base queryset for ChiTietHoaDonModel with related objects
        """
        return self.model_class.objects.all().select_related(
            'hoa_don',
            'ma_dv',
            'tk_dt',
            'dvt',
            'tk_ck',
            'ma_thue',
            'tk_thue_co',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_ku',
            'ma_phi',
            'ma_cp0',
        )

    def get_for_hoa_don(self, hoa_don_uuid: str) -> QuerySet:  # noqa: C901
        """
        Returns a queryset of ChiTietHoaDonModel for a specific HoaDonDichVuModel.

        Parameters
        ----------
        hoa_don_uuid : str
            The UUID of the HoaDonDichVuModel to filter by

        Returns
        -------
        QuerySet
            Queryset of ChiTietHoaDonModel for the HoaDonDichVuModel
        """
        return self.get_queryset().filter(hoa_don__uuid=hoa_don_uuid)

    def get_by_uuid(self, uuid: str) -> ChiTietHoaDonModel:  # noqa: C901
        """
        Returns a ChiTietHoaDonModel by its UUID.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietHoaDonModel to retrieve

        Returns
        -------
        ChiTietHoaDonModel
            The ChiTietHoaDonModel with the specified UUID
        """
        return get_object_or_404(self.get_queryset(), uuid=uuid)

    def create(
        self, hoa_don: HoaDonDichVuModel, **kwargs
    ) -> ChiTietHoaDonModel:  # noqa: C901
        """
        Creates a new ChiTietHoaDonModel.

        Parameters
        ----------
        hoa_don : HoaDonDichVuModel
            The HoaDonDichVuModel to associate with the ChiTietHoaDonModel
        **kwargs
            Additional fields for the ChiTietHoaDonModel

        Returns
        -------
        ChiTietHoaDonModel
            The created ChiTietHoaDonModel
        """
        chi_tiet = ChiTietHoaDonModel(hoa_don=hoa_don, **kwargs)
        chi_tiet.save()
        return chi_tiet

    def update(self, uuid: str, **kwargs) -> ChiTietHoaDonModel:  # noqa: C901
        """
        Updates an existing ChiTietHoaDonModel.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietHoaDonModel to update
        **kwargs
            Fields to update

        Returns
        -------
        ChiTietHoaDonModel
            The updated ChiTietHoaDonModel
        """
        chi_tiet = self.get_by_uuid(uuid)
        for key, value in kwargs.items():
            setattr(chi_tiet, key, value)
        chi_tiet.save()
        return chi_tiet

    def delete(self, uuid: str) -> None:  # noqa: C901
        """
        Deletes a ChiTietHoaDonModel.

        Parameters
        ----------
        uuid : str
            The UUID of the ChiTietHoaDonModel to delete
        """
        chi_tiet = self.get_by_uuid(uuid)
        chi_tiet.delete()

    def delete_for_hoa_don(self, hoa_don_uuid: str) -> None:  # noqa: C901
        """
        Deletes all ChiTietHoaDonModel for a specific HoaDonDichVuModel.

        Parameters
        ----------
        hoa_don_uuid : str
            The UUID of the HoaDonDichVuModel to delete ChiTietHoaDonModel for
        """
        self.get_for_hoa_don(hoa_don_uuid).delete()
