"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for PhieuThuChiTiet model.
"""

from typing import Any, Dict, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models import PhieuThuChiTietModel, PhieuThuModel  # noqa: F401,
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuThuChiTietRepository(BaseRepository):
    """
    Repository class for handling PhieuThuChiTietModel database operations.
    Implements the Repository pattern for PhieuThuChiTietModel.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the PhieuThuChiTietModel.
        """
        super().__init__(model_class=PhieuThuChiTietModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for PhieuThuChiTietModel.

        Returns
        -------
        QuerySet
            The base queryset for PhieuThuChiTietModel.
        """
        return self.model_class.objects.all().select_related(
            'phieu_thu',
            'ma_kh',
            'id_hd',
            'tk_co',
            'ma_bp',
            'ma_vv',
            'ma_hd',
            'ma_dtt',
            'ma_ku',
            'ma_phi',
            'ma_sp',
            'ma_cp0',
        )

    def get_by_uuid(self, uuid: Union[str, UUID]) -> PhieuThuChiTietModel:  # noqa: C901
        """
        Gets a PhieuThuChiTietModel instance by UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the PhieuThuChiTietModel.

        Returns
        -------
        PhieuThuChiTietModel
            The PhieuThuChiTietModel instance.

        Raises
        ------
        PhieuThuChiTietModel.DoesNotExist
            If the instance does not exist.
        """
        return self.get_queryset().get(uuid=uuid)

    def list_by_parent(self, parent_uuid: Union[str, UUID]) -> QuerySet:  # noqa: C901
        """
        Lists PhieuThuChiTietModel instances for a specific parent.

        Parameters
        ----------
        parent_uuid : Union[str, UUID]
            The UUID of the parent PhieuThuModel.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuThuChiTietModel instances.
        """
        return self.model_class.objects.filter(phieu_thu__uuid=parent_uuid).order_by(
            'line'
        )

    def create(
        self, parent_field: PhieuThuModel, data: Dict[str, Any]
    ) -> PhieuThuChiTietModel:  # noqa: C901
        """
        Creates a new PhieuThuChiTietModel instance.

        Parameters
        ----------
        parent_field : PhieuThuModel
            The parent PhieuThuModel instance.
        data : Dict[str, Any]
            The data for the new PhieuThuChiTietModel.

        Returns
        -------
        PhieuThuChiTietModel
            The created PhieuThuChiTietModel instance.
        """
        # Convert UUID strings to model instances
        data_copy = self.convert_uuids_to_model_instances(data)
        # Create the instance
        instance = self.model_class(phieu_thu=parent_field, **data_copy)
        instance.save()

        return instance

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> PhieuThuChiTietModel:  # noqa: C901
        """
        Updates an existing PhieuThuChiTietModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the PhieuThuChiTietModel to update.
        data : Dict[str, Any]
            The data to update.

        Returns
        -------
        PhieuThuChiTietModel
            The updated PhieuThuChiTietModel instance.

        Raises
        ------
        PhieuThuChiTietModel.DoesNotExist
            If the instance does not exist.
        """
        instance = self.model_class.objects.get(uuid=uuid)
        # Update fields
        for key, value in data.items():
            if key != 'phieu_thu':  # Don't update the parent reference
                setattr(instance, key, value)

        instance.save()
        return instance

    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuThuChiTietModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the PhieuThuChiTietModel to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.

        Raises
        ------
        PhieuThuChiTietModel.DoesNotExist
            If the instance does not exist.
        """
        instance = self.model_class.objects.get(uuid=uuid)
        instance.delete()
        return True
