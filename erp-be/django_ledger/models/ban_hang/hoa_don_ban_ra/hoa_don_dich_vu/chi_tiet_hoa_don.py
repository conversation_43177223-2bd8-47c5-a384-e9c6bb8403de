"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiTietHoaDonModel, which represents the Service Invoice Detail  # noqa: E501
which is associated with the HoaDonDichVuModel.
"""

import uuid  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu.hoa_don_dich_vu import (  # noqa: F401,
    HoaDonDichVuModel,
)
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietHoaDonModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietHoaDonModel database will inherit from.  # noqa: E501
    The ChiTietHoaDonModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        Primary key, unique identifier.
    hoa_don : ForeignKey
        Related service invoice.
    line : int
        Line number in the invoice.
    ma_dv : str
        Service code.
    tk_dt : str
        Revenue account.
    dvt : str
        Unit of measurement.
    so_luong : decimal
        Quantity of service.
    gia_nt2 : decimal
        Price in foreign currency.
    tien_nt2 : decimal
        Amount in foreign currency.
    dien_giai : str
        Detailed description of the service.
    tl_ck : decimal
        Discount rate.
    ck_nt : decimal
        Discount in foreign currency.
    tk_ck : str
        Discount account.
    ten_tk_ck : str
        Discount account name.
    ma_thue : str
        Tax code.
    thue_suat : decimal
        Tax rate.
    tk_thue_co : str
        Tax credit account.
    ten_tk_thue_co : str
        Tax credit account name.
    thue_nt : decimal
        Tax in foreign currency.
    gia2 : decimal
        Price.
    tien2 : decimal
        Amount.
    ck : decimal
        Discount.
    thue : decimal
        Tax.
    ma_bp : str
        Department code.
    ma_vv : str
        Case code.
    ma_hd : str
        Contract code.
    ma_dtt : str
        Aggregation object code.
    ma_ku : str
        Area code.
    ma_phi : str
        Fee code.
    ma_sp : str
        Product code.
    ma_lsx : str
        Production order code.
    ma_cp0 : str
        Cost code.
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        verbose_name=_("UUID"),
        help_text=_("Unique identifier for the record"),
    )

    hoa_don = models.ForeignKey(
        HoaDonDichVuModel,
        on_delete=models.CASCADE,
        related_name='chi_tiet',
        verbose_name=_("Hóa đơn dịch vụ"),
        help_text=_("Hóa đơn dịch vụ liên quan"),
    )

    line = models.IntegerField(
        verbose_name=_("Số thứ tự dòng"),
        help_text=_("Số thứ tự dòng trong hóa đơn"),
    )

    ma_dv = models.ForeignKey(
        "django_ledger.DichVuModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã dịch vụ"),
        help_text=_("Mã dịch vụ được bán"),
        related_name="chi_tiet_hoa_don_dv",
    )

    tk_dt = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        verbose_name=_("Tài khoản doanh thu"),
        help_text=_("Tài khoản doanh thu liên quan"),
        related_name="chi_tiet_hoa_don_dt",
    )

    dvt = models.ForeignKey(
        "django_ledger.DonViTinhModel",
        on_delete=models.CASCADE,
        verbose_name=_("Đơn vị tính"),
        help_text=_("Đơn vị tính của dịch vụ"),
        related_name="chi_tiet_hoa_don_dvt",
    )

    so_luong = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Số lượng"),
        help_text=_("Số lượng dịch vụ"),
    )

    gia_nt2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Giá ngoại tệ"),
        help_text=_("Giá ngoại tệ của dịch vụ"),
    )

    tien_nt2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tiền ngoại tệ"),
        help_text=_("Tiền ngoại tệ của dịch vụ"),
    )

    dien_giai = models.CharField(
        max_length=255,
        verbose_name=_("Diễn giải"),
        help_text=_("Diễn giải chi tiết về dịch vụ"),
    )

    tl_ck = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tỷ lệ chiết khấu"),
        help_text=_("Tỷ lệ chiết khấu áp dụng"),
    )

    ck_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Chiết khấu ngoại tệ"),
        help_text=_("Chiết khấu ngoại tệ"),
    )

    tk_ck = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Tài khoản chiết khấu"),
        help_text=_("Tài khoản chiết khấu liên quan"),
        related_name="chi_tiet_hoa_don_ck",
    )

    ten_tk_ck = models.CharField(
        max_length=255,
        verbose_name=_("Tên tài khoản chiết khấu"),
        help_text=_("Tên tài khoản chiết khấu"),
    )

    ma_thue = models.ForeignKey(
        "django_ledger.TaxModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã thuế"),
        help_text=_("Mã thuế áp dụng cho dịch vụ"),
        related_name="chi_tiet_hoa_don_thue",
    )

    thue_suat = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Thuế suất"),
        help_text=_("Thuế suất áp dụng"),
    )

    tk_thue_co = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Tài khoản thuế có"),
        help_text=_("Tài khoản thuế có liên quan"),
        related_name="chi_tiet_hoa_don_thue_co",
    )

    ten_tk_thue_co = models.CharField(
        max_length=255,
        verbose_name=_("Tên tài khoản thuế có"),
        help_text=_("Tên tài khoản thuế có"),
    )

    thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Thuế ngoại tệ"),
        help_text=_("Thuế ngoại tệ"),
    )

    gia2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Giá"),
        help_text=_("Giá của dịch vụ"),
    )

    tien2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tiền"),
        help_text=_("Tiền của dịch vụ"),
    )

    ck = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Chiết khấu"),
        help_text=_("Chiết khấu"),
    )

    thue = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Thuế"),
        help_text=_("Thuế"),
    )

    ma_bp = models.ForeignKey(
        "django_ledger.BoPhanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã bộ phận"),
        help_text=_("Mã bộ phận liên quan"),
        related_name="chi_tiet_hoa_don_bp",
    )

    ma_vv = models.ForeignKey(
        "django_ledger.VuViecModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã vụ việc"),
        help_text=_("Mã vụ việc liên quan"),
        related_name="chi_tiet_hoa_don_vv",
    )

    ma_hd = models.ForeignKey(
        "django_ledger.ContractModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã hợp đồng"),
        help_text=_("Mã hợp đồng liên quan"),
        related_name="chi_tiet_hoa_don_hd",
    )

    ma_dtt = models.CharField(
        max_length=20,
        blank=True,
        verbose_name=_("Mã đối tượng tập hợp"),
        help_text=_("Mã đối tượng tập hợp liên quan"),
    )

    ma_ku = models.ForeignKey(
        "django_ledger.KheUocModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã khe ước"),
        help_text=_("Mã khe ước liên quan"),
        related_name="chi_tiet_hoa_don_ku",
    )

    ma_phi = models.ForeignKey(
        "django_ledger.PhiModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã phí"),
        help_text=_("Mã phí liên quan"),
        related_name="chi_tiet_hoa_don_phi",
    )

    ma_sp = models.CharField(
        max_length=50,
        verbose_name=_("Mã sản phẩm"),
        help_text=_("Mã sản phẩm liên quan"),
    )

    ma_lsx = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Mã lệnh sản xuất"),
        help_text=_("Mã lệnh sản xuất liên quan"),
    )

    ma_cp0 = models.ForeignKey(
        "django_ledger.ChiPhiModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã chi phí"),
        help_text=_("Mã chi phí liên quan"),
        related_name="chi_tiet_hoa_don_cp0",
    )

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=['hoa_don']),
            models.Index(fields=['ma_dv']),
            models.Index(fields=['line']),
            models.Index(fields=['created']),
            models.Index(fields=['updated']),
        ]
        ordering = ['hoa_don', 'line']

    def __str__(self):  # noqa: C901
        hoa_don_so = self.hoa_don.so_ct if self.hoa_don and hasattr(self.hoa_don, 'so_ct') else 'N/A'
        return f'Chi tiết hóa đơn: {hoa_don_so} - Dòng {self.line}'


class ChiTietHoaDonModel(ChiTietHoaDonModelAbstract):
    """
    Base ChiTietHoaDon Model Implementation
    """

    class Meta(ChiTietHoaDonModelAbstract.Meta):
        abstract = False
        verbose_name = _('Chi tiết hóa đơn dịch vụ')
        verbose_name_plural = _('Chi tiết hóa đơn dịch vụ')
        db_table = 'chi_tiet_hoa_don'
