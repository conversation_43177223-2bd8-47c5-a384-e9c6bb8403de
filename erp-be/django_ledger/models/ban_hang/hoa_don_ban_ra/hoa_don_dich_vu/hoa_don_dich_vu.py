"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the HoaDonDichVuModel, which represents the Service Invoice
which the EntityModel issues to its customers for the supply of services.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.entity import EntityModel  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class HoaDonDichVuModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the HoaDonDichVuModel database will inherit from.  # noqa: E501
    The HoaDonDichVuModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        Primary key, unique identifier.
    ma_kh : str
        Customer code.
    ma_so_thue : str
        Tax code of the customer.
    pt_tao_yn : bool
        Creation method (True: automatic, False: manual).
    ma_httt : str
        Payment method code (string).
    ten_kh_thue : str
        Customer name on tax invoice.
    dia_chi : str
        Customer address.
    ong_ba : str
        Customer representative name.
    ma_nvbh : str
        Sales staff code.
    e_mail : str
        Customer contact email.
    tk : str
        Related accounting account.
    ma_tt : str
        Status code.
    dien_giai : str
        Detailed description of the invoice.
    ma_ngv : str
        Supervisor code.
    unit_id : int
        Unit ID related to the invoice.
    i_so_ct : str
        Internal document number.
    ma_nk : str
        Transaction code.
    so_ct : str
        Document number.
    ngay_ct : date
        Document date.
    ngay_lct : date
        Document creation date.
    so_ct2 : str
        Second document number (if any).
    ma_nt : str
        Currency code.
    ty_gia : decimal
        Exchange rate.
    status : str
        Invoice status.
    transfer_yn : bool
        Mark if the invoice has been transferred.
    ma_kh9 : str
        Secondary customer code (if any).
    ly_do_huy : str
        Cancellation reason (if any).
    ly_do : str
        Reason related to the invoice.
    ten_vt_thue : str
        Material name on tax invoice.
    ghi_chu : str
        Additional notes about the invoice.
    ma_tthddt : str
        Electronic invoice status code.
    ma_pttt : str
        Payment method code.
    so_ct_hddt : str
        Electronic invoice document number.
    ngay_ct_hddt : date
        Electronic invoice document date.
    so_ct2_hddt : str
        Second electronic invoice document number.
    ma_mau_ct_hddt : str
        Electronic invoice document template code.
    t_tien_nt2 : decimal
        Total amount in foreign currency.
    t_tien2 : decimal
        Total amount.
    t_thue_nt : decimal
        Total tax in foreign currency.
    t_thue : decimal
        Total tax.
    t_ck_nt : decimal
        Total discount in foreign currency.
    t_ck : decimal
        Total discount.
    t_tt_nt : decimal
        Total payment in foreign currency.
    t_tt : decimal
        Total payment.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)

    entity_model = models.ForeignKey(
        EntityModel,
        on_delete=models.CASCADE,
        verbose_name=_("Entity Model"),
        related_name="hoa_don_dich_vu",
        help_text=_("The entity this invoice belongs to"),
    )

    ma_kh = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.CASCADE,
        verbose_name=_("Mã khách hàng"),
        help_text=_("Mã khách hàng liên quan đến hóa đơn dịch vụ"),
        related_name="hoa_don_dich_vu_kh",
    )

    ma_so_thue = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Mã số thuế"),
        help_text=_("Mã số thuế của khách hàng"),
    )

    pt_tao_yn = models.BooleanField(
        default=True,
        verbose_name=_("Phương thức tạo"),
        help_text=_("Phương thức tạo hóa đơn (True: tự động, False: thủ công)"),
    )

    ma_httt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã hình thức thanh toán"),
        help_text=_("Mã hình thức thanh toán cho hóa đơn"),
    )

    ten_kh_thue = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên khách hàng thuế"),
        help_text=_("Tên khách hàng trên hóa đơn thuế"),
    )

    dia_chi = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Địa chỉ"),
        help_text=_("Địa chỉ khách hàng"),
    )

    ong_ba = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name=_("Ông/Bà"),
        help_text=_("Tên người đại diện khách hàng"),
    )

    ma_nvbh = models.ForeignKey(
        "django_ledger.NhanVienModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã nhân viên bán hàng"),
        help_text=_("Mã nhân viên bán hàng phụ trách"),
        related_name="hoa_don_dich_vu_nvbh",
    )

    e_mail = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Email"),
        help_text=_("Email liên hệ của khách hàng"),
    )

    tk = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        verbose_name=_("Tài khoản"),
        help_text=_("Tài khoản kế toán liên quan"),
        related_name="hoa_don_dich_vu_tk",
    )

    ma_tt = models.ForeignKey(
        "django_ledger.HanThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã trạng thái"),
        help_text=_("Mã trạng thái của hóa đơn"),
        related_name="hoa_don_dich_vu_tt",
    )

    dien_giai = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Diễn giải"),
        help_text=_("Diễn giải chi tiết về hóa đơn"),
    )

    ma_ngv = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã người giám sát"),
        help_text=_("Mã người giám sát hóa đơn"),
    )

    unit_id = models.ForeignKey(
        "django_ledger.EntityUnitModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("ID đơn vị"),
        help_text=_("ID đơn vị liên quan đến hóa đơn"),
        related_name="hoa_don_dich_vu_unit",
    )

    i_so_ct = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Số chứng từ nội bộ"),
        help_text=_("Số chứng từ nội bộ của hóa đơn"),
    )

    ma_nk = models.ForeignKey(
        "django_ledger.QuyenChungTu",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã nghiệp vụ"),
        help_text=_("Mã nghiệp vụ liên quan đến hóa đơn"),
        related_name="hoa_don_dich_vu_nk",
    )

    so_ct = models.ForeignKey(
        "django_ledger.ChungTu",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Số chứng từ"),
        help_text=_("Số chứng từ của hóa đơn"),
        related_name="hoa_don_dich_vu_ct",
    )

    ngay_ct = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Ngày chứng từ"),
        help_text=_("Ngày phát hành chứng từ"),
    )

    ngay_lct = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Ngày lập chứng từ"),
        help_text=_("Ngày lập chứng từ"),
    )

    so_ct2 = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name=_("Số chứng từ 2"),
        help_text=_("Số chứng từ thứ hai (nếu có)"),
    )

    ma_nt = models.ForeignKey(
        "django_ledger.NgoaiTeModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã ngoại tệ"),
        help_text=_("Mã ngoại tệ sử dụng trong hóa đơn"),
        related_name="hoa_don_dich_vu_nt",
    )

    ty_gia = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tỷ giá"),
        help_text=_("Tỷ giá quy đổi ngoại tệ"),
    )

    status = models.CharField(
        max_length=2,
        verbose_name=_("Trạng thái"),
        help_text=_("Trạng thái của hóa đơn"),
    )

    transfer_yn = models.BooleanField(
        default=False,
        verbose_name=_("Đã chuyển"),
        help_text=_("Đánh dấu hóa đơn đã được chuyển hay chưa"),
    )

    ma_kh9 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã khách hàng 9"),
        help_text=_("Mã khách hàng phụ (nếu có)"),
    )

    ly_do_huy = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Lý do hủy"),
        help_text=_("Lý do hủy hóa đơn (nếu có)"),
    )

    ly_do = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Lý do"),
        help_text=_("Lý do liên quan đến hóa đơn"),
    )

    ten_vt_thue = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên vật tư thuế"),
        help_text=_("Tên vật tư trên hóa đơn thuế"),
    )

    ghi_chu = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Ghi chú"),
        help_text=_("Ghi chú thêm về hóa đơn"),
    )

    ma_tthddt = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("Mã trạng thái hóa đơn điện tử"),
        help_text=_("Mã trạng thái hóa đơn điện tử"),
    )

    ma_pttt = models.ForeignKey(
        "django_ledger.PhuongThucThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã phương thức thanh toán"),
        help_text=_("Mã phương thức thanh toán"),
        related_name="hoa_don_dich_vu_pttt",
    )

    so_ct_hddt = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Số chứng từ HĐĐT"),
        help_text=_("Số chứng từ hóa đơn điện tử"),
    )

    ngay_ct_hddt = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Ngày chứng từ HĐĐT"),
        help_text=_("Ngày chứng từ hóa đơn điện tử"),
    )

    so_ct2_hddt = models.TextField(
        null=True,
        blank=True,
        verbose_name=_("Số chứng từ 2 HĐĐT"),
        help_text=_("Số chứng từ thứ hai của hóa đơn điện tử"),
    )

    ma_mau_ct_hddt = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name=_("Mã mẫu chứng từ HĐĐT"),
        help_text=_("Mã mẫu chứng từ hóa đơn điện tử"),
    )

    t_tien_nt2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tổng tiền ngoại tệ"),
        help_text=_("Tổng tiền ngoại tệ của hóa đơn"),
    )

    t_tien2 = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tổng tiền"),
        help_text=_("Tổng tiền của hóa đơn"),
    )

    t_thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tổng thuế ngoại tệ"),
        help_text=_("Tổng thuế ngoại tệ của hóa đơn"),
    )

    t_thue = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tổng thuế"),
        help_text=_("Tổng thuế của hóa đơn"),
    )

    t_ck_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tổng chiết khấu ngoại tệ"),
        help_text=_("Tổng chiết khấu ngoại tệ của hóa đơn"),
    )

    t_ck = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tổng chiết khấu"),
        help_text=_("Tổng chiết khấu của hóa đơn"),
    )

    t_tt_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tổng thanh toán ngoại tệ"),
        help_text=_("Tổng thanh toán ngoại tệ của hóa đơn"),
    )

    t_tt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        verbose_name=_("Tổng thanh toán"),
        help_text=_("Tổng thanh toán của hóa đơn"),
    )

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=["ma_kh"]),
            models.Index(fields=["so_ct"]),
            models.Index(fields=["ngay_ct"]),
            models.Index(fields=["status"]),
            models.Index(fields=["ma_httt"]),
            models.Index(fields=["ma_nvbh"]),
            models.Index(fields=["ma_nk"]),
            models.Index(fields=["created"]),
            models.Index(fields=["updated"]),
        ]

    def __str__(self):  # noqa: C901
        return f"Hóa đơn dịch vụ: {self.so_ct} - {self.ten_kh_thue}"


class HoaDonDichVuModel(HoaDonDichVuModelAbstract):
    """
    Base HoaDonDichVu Model Implementation
    """

    class Meta(HoaDonDichVuModelAbstract.Meta):
        abstract = False
        verbose_name = _("Hóa đơn dịch vụ")
        verbose_name_plural = _("Hóa đơn dịch vụ")
        db_table = "hoa_don_dich_vu"
