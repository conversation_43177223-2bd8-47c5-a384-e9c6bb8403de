"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuThu (Receipt Voucher) Model
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class PhieuThuModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the PhieuThuModel.
    """

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuThuModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug used for filtering the QuerySet.

        Returns
        -------
        PhieuThuModelQueryset
            A QuerySet of PhieuThuModel with applied filters.
        """
        return self.filter(entity_model__slug=entity_slug)

    def active(self):  # noqa: C901
        """
        Returns active PhieuThuModel.

        Returns
        -------
        PhieuThuModelQueryset
            A QuerySet of active PhieuThuModel.
        """
        return self.filter(status="1")

    def inactive(self):  # noqa: C901
        """
        Returns inactive PhieuThuModel.

        Returns
        -------
        PhieuThuModelQueryset
            A QuerySet of inactive PhieuThuModel.
        """
        return self.filter(status="0")


class PhieuThuModelManager(Manager):
    """
    A custom defined PhieuThuModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    PhieuThuModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom QuerySet for the PhieuThuModel.
        """
        return PhieuThuModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuThuModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The EntityModel slug used for filtering the QuerySet.

        Returns
        -------
        PhieuThuModelQueryset
            A QuerySet of PhieuThuModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)

    def active(self):  # noqa: C901
        """
        Returns active PhieuThuModel.

        Returns
        -------
        PhieuThuModelQueryset
            A QuerySet of active PhieuThuModel.
        """
        return self.get_queryset().active()

    def inactive(self):  # noqa: C901
        """
        Returns inactive PhieuThuModel.

        Returns
        -------
        PhieuThuModelQueryset
            A QuerySet of inactive PhieuThuModel.
        """
        return self.get_queryset().inactive()


class PhieuThuModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuThuModel database will inherit from.
    The PhieuThuModel inherits functionality from the following MixIns:

        1. :func:`ChungTuMixIn <django_ledger.models._mixins.chung_tu_mixins.ChungTuMixIn>`
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    entity_model: EntityModel
        The EntityModel this PhieuThuModel belongs to.

    id: int
        ID of the receipt voucher.

    ma_ngv: str
        Business operation code.

    dia_chi: str
        Address.

    ong_ba: str
        Person name.

    dien_giai: str
        Description.

    tk: AccountModel
        Account.

    unit_id: EntityUnitModel
        Entity unit.

    ma_nt: NgoaiTeModel
        Currency.

    ty_gia: Decimal
        Exchange rate.

    status: str
        Status.

    so_ct0: str
        Original document number.

    ngay_ct0: date
        Original document date.

    so_ct_goc: int
        Original document number.

    dien_giai_ct_goc: str
        Original document description.

    ma_tt: HanThanhToanModel
        Payment term.

    ma_kh: CustomerModel
        Customer.

    t_tien_nt: Decimal
        Total amount in foreign currency.

    t_tien: Decimal
        Total amount.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    entity_model = models.ForeignKey(
        "django_ledger.EntityModel",
        on_delete=models.CASCADE,
        verbose_name=_("Entity Model"),
    )
    id = models.IntegerField(null=True, blank=True, verbose_name=_("ID"))
    ma_ngv = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã người giao vốn"),
    )
    dia_chi = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("Địa chỉ")
    )
    ong_ba = models.CharField(
        max_length=100, null=True, blank=True, verbose_name=_("Ông/Bà")
    )
    dien_giai = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("Diễn giải")
    )
    tk = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.CASCADE,
        related_name="phieu_thu_tk",
        verbose_name=_("Tài khoản"),
    )
    unit_id = models.ForeignKey(
        "django_ledger.EntityUnitModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Đơn vị"),
    )
    # i_so_ct field is provided by ChungTuMixIn
    # ma_nk field is provided by ChungTuMixIn
    # so_ct field is provided by ChungTuMixIn
    # ngay_ct and ngay_lct fields are provided by ChungTuMixIn
    ma_nt = models.ForeignKey(
        "django_ledger.NgoaiTeModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã ngoại tệ"),
    )
    ty_gia = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tỷ giá"),
    )
    status = models.CharField(max_length=10, default="1", verbose_name=_("Trạng thái"))
    so_ct0 = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Số chứng từ gốc"),
    )
    ngay_ct0 = models.DateField(
        null=True, blank=True, verbose_name=_("Ngày chứng từ gốc")
    )
    so_ct_goc = models.IntegerField(
        null=True, blank=True, verbose_name=_("Số chứng từ gốc")
    )
    dien_giai_ct_goc = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Diễn giải chứng từ gốc"),
    )
    ma_tt = models.ForeignKey(
        "django_ledger.HanThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã hạn thanh toán"),
    )
    ma_kh = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã khách hàng"),
    )
    t_tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng tiền ngoại tệ"),
    )
    t_tien = models.DecimalField(
        max_digits=18,
        decimal_places=4,
        default=0,
        verbose_name=_("Tổng tiền"),
    )

    # Ledger relationship for 1:1 mapping with receipt voucher
    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho phiếu thu này"),
        related_name="phieu_thu"
    )

    objects = PhieuThuModelManager.from_queryset(PhieuThuModelQueryset)()

    class Meta:
        abstract = True
        verbose_name = _("Phiếu Thu")
        verbose_name_plural = _("Phiếu Thu")
        indexes = [
            models.Index(fields=["entity_model"]),
            models.Index(fields=["unit_id"]),
            models.Index(fields=["tk"]),
            models.Index(fields=["ma_kh"]),
            models.Index(fields=["ngay_ct"]),
            models.Index(fields=["status"]),
            models.Index(fields=["ledger"]),
        ]

    def __str__(self):  # noqa: C901
        return f"{self.i_so_ct or ''} - {self.dien_giai or ''}"


class PhieuThuModel(PhieuThuModelAbstract):
    """
    Base PhieuThu Model Implementation
    """

    class Meta(PhieuThuModelAbstract.Meta):
        abstract = False
        db_table = "phieu_thu"
