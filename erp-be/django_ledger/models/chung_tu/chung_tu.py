import uuid

from django.db import models
from django.db.models import QuerySet
from django.utils.translation import gettext_lazy as _

from django_ledger.models.mixins import CreateUpdateMixIn


class ChungTuQuerySet(QuerySet):
    def for_entity(self, entity_slug, user_model):  # noqa: C901
        return self.filter(
            entity_model__slug=entity_slug, entity_model__admin=user_model
        )

    def active(self):  # noqa: C901
        """Get active chung tu."""
        return self.filter(df_status='5')

    def by_ma_ct(self, ma_ct):  # noqa: C901
        """Filter chung tu by ma_ct."""
        return self.filter(ma_ct=ma_ct)

    def by_order_type(self, order_type):  # noqa: C901
        """Filter chung tu by order_type."""
        return self.filter(order_type=order_type)

    def need_kt_ton(self):  # noqa: C901
        """Get chung tu that needs kiem tra ton."""
        return self.filter(ct_kt_ton='1')

    def su_dung_vi_tri(self):  # noqa: C901
        """Get chung tu that uses vi tri."""
        return self.filter(ct_sd_vi_tri=True)


class ChungTuManager(models.Manager):
    def for_entity(self, entity_slug, user_model) -> ChungTuQuerySet:  # noqa: C901
        return self.get_queryset().for_entity(entity_slug, user_model)

    def get_queryset(self):  # noqa: C901
        """Return custom ChungTuQuerySet."""
        return ChungTuQuerySet(self.model, using=self._db)

    def for_user(self, user_model):  # noqa: C901
        """Get chung tu for user."""
        return self.get_queryset().filter(entity_model__admin=user_model)

    def active(self):  # noqa: C901
        """Get active chung tu."""
        return self.get_queryset().active()

    def by_ma_ct(self, ma_ct):  # noqa: C901
        """Get chung tu by ma_ct."""
        return self.get_queryset().by_ma_ct(ma_ct)

    def by_order_type(self, order_type):  # noqa: C901
        """Get chung tu by order_type."""
        return self.get_queryset().by_order_type(order_type)

    def need_kt_ton(self):  # noqa: C901
        """Get chung tu that needs kiem tra ton."""
        return self.get_queryset().need_kt_ton()

    def su_dung_vi_tri(self):  # noqa: C901
        """Get chung tu that uses vi tri."""
        return self.get_queryset().su_dung_vi_tri()


class ChungTuAbstractModel(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChungTuModel database will inherit from.
    The ChungTuModel inherits functionality from the following MixIns:
        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`
    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().
    entity_model : ForeignKey
        A foreign key to the EntityModel. This links the document type to a specific entity.
    ma_ct : str
        Document type code (max 10 characters).
    ten_ct : str
        Document type name (max 255 characters).
    ten_ct2 : str
        Document type name in English (max 255 characters, optional).
    ten_ct3 : str
        Document type name in English 2 (max 255 characters, optional).
    ngay_ks : date
        Start date for using this document type.
    stt : int
        Sort order number.
    i_so_ct : int
        Number of documents.
    d_page_count : int
        Page count (default 0).
    order_type : str
        Sort type (max 1 character, default '0').
    df_status : str
        Default status (max 1 character, default '5').
    ct_kt_ton : str
        Document inventory check (max 1 character, default '0').
    loai_dl_ton : str
        Inventory data type (max 1 character, default '0').
    ct_sd_vi_tri : bool
        Whether document uses position (default False).
    ngay_lct_yn : bool
        Show document creation date (default True).
    vc_link : str
        Document link (max 255 characters, optional).
    ct_save_log : str
        Save document log (max 1 character, default '0').
    xcode : str
        Document type category (max 255 characters, with choices).
    """

    # Document type choices
    TYPE_TIEN_MAT = 'tien_mat'
    TYPE_TIEN_GUI = 'tien_gui'
    TYPE_BAN_HANG = 'ban_hang'
    TYPE_HOA_DON = 'hoa_don'
    TYPE_MUA_HANG = 'mua_hang'
    TYPE_TON_KHO = 'ton_kho'
    TYPE_GIA_THANH = 'gia_thanh'
    TYPE_THUE = 'thue'
    TYPE_TONG_HOP = 'tong_hop'
    TYPE_BAN_HANG_2 = 'ban_hang_2'
    TYPE_E_PROCUREMENT = 'e_procurement'
    TYPE_KHO = 'kho'
    TYPE_NHAN_SU = 'nhan_su'
    TYPE_HOA_DON_DAU_VAO = 'hoa_don_dau_vao'
    TYPE_CHOICES = [
        (TYPE_TIEN_MAT, _('Tiền mặt')),
        (TYPE_TIEN_GUI, _('Tiền gửi')),
        (TYPE_BAN_HANG, _('Bán hàng')),
        (TYPE_HOA_DON, _('Hóa đơn')),
        (TYPE_MUA_HANG, _('Mua hàng')),
        (TYPE_TON_KHO, _('Tồn kho')),
        (TYPE_GIA_THANH, _('Giá thành')),
        (TYPE_THUE, _('Thuế')),
        (TYPE_TONG_HOP, _('Tổng hợp')),
        (TYPE_BAN_HANG_2, _('Bán hàng 2')),
        (TYPE_E_PROCUREMENT, _('e-Procurement')),
        (TYPE_KHO, _('Kho')),
        (TYPE_NHAN_SU, _('Nhân sự')),
        (TYPE_HOA_DON_DAU_VAO, _('Hóa đơn đầu vào')),
    ]

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity Model'),
    )
    ma_ct = models.CharField(max_length=10, verbose_name=_("Mã chứng từ"))
    ten_ct = models.CharField(max_length=255, verbose_name=_("Tên chứng từ"))
    ten_ct2 = models.CharField(
        max_length=255, blank=True, verbose_name=_("Tên chứng từ (Tiếng Anh)")
    )
    ten_ct3 = models.CharField(
        max_length=255, blank=True, verbose_name=_("Tên chứng từ (Tiếng Anh 2)")
    )
    ngay_ks = models.DateField(verbose_name=_("Ngày khởi sử dụng"))
    stt = models.IntegerField(verbose_name=_("Số thứ tự"))
    i_so_ct = models.IntegerField(verbose_name=_("Số lượng chứng từ"))
    d_page_count = models.IntegerField(default=0, verbose_name=_("Số trang"))
    order_type = models.CharField(
        max_length=1, default='0', verbose_name=_("Loại sắp xếp")
    )
    df_status = models.CharField(
        max_length=1, default='5', verbose_name=_("Trạng thái mặc định")
    )
    ct_kt_ton = models.CharField(
        max_length=1, default='0', verbose_name=_("Chứng từ kiểm tra tồn")
    )
    loai_dl_ton = models.CharField(
        max_length=1, default='0', verbose_name=_("Loại dữ liệu tồn")
    )
    ct_sd_vi_tri = models.BooleanField(
        default=False, verbose_name=_("Chứng từ sử dụng vị trí")
    )
    ngay_lct_yn = models.BooleanField(
        default=True, verbose_name=_("Hiển thị ngày lập chứng từ")
    )
    vc_link = models.CharField(
        max_length=255, blank=True, verbose_name=_("Liên kết văn bản")
    )
    ct_save_log = models.CharField(
        max_length=1, default='0', verbose_name=_("Lưu log chứng từ")
    )
    xcode = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_("Loại chứng từ"),
        choices=TYPE_CHOICES,
    )
    user_id0_yn = models.BooleanField(
        default=False, verbose_name=_("Hiển thị thông tin người tạo")
    )
    user_id2_yn = models.BooleanField(
        default=False, verbose_name=_("Hiển thị thông tin người sửa gần nhất")
    )
    objects = ChungTuManager.from_queryset(ChungTuQuerySet)()

    class Meta:
        db_table = 'chung_tu'
        verbose_name = _('Chứng từ')
        verbose_name_plural = _('Chứng từ')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_ct']),
        ]
        unique_together = [('entity_model', 'ma_ct')]

    def __str__(self):  # noqa: C901
        return f'{self.ma_ct}: {self.ten_ct}'

    def clean(self):  # noqa: C901
        """Perform model validation."""
        super().clean()
        # Add custom validation logic here if needed

    def save(self, *args, **kwargs):  # noqa: C901
        """Override save method to add custom logic."""
        # Add pre-save logic here if needed
        super().save(*args, **kwargs)
        # Add post-save logic here if needed


class ChungTu(ChungTuAbstractModel):
    """
    Concrete implementation of Chung Tu model.
    """

    class Meta(ChungTuAbstractModel.Meta):
        abstract = False
