"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

A Bank Account refers to the financial institution which holds financial assets for the EntityModel.  # noqa: E501
A bank account usually holds cash, which is a Current Asset. Transactions may be imported using the open financial  # noqa: E501
format specification OFX into a staging area for final disposition into the EntityModel ledger.  # noqa: E501
"""

from typing import Optional  # noqa: F401
from uuid import uuid4  # noqa: F401

from django.contrib.auth import get_user_model  # noqa: F401,
from django.core.exceptions import ValidationError  # noqa: F401,
from django.db import models  # noqa: F401,
from django.db.models import Q, QuerySet  # noqa: F401,
from django.shortcuts import get_object_or_404  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import (  # noqa: F401,
    CreateUpdateMixIn,
    FinancialAccountInfoMixin,
)
from django_ledger.models.utils import lazy_loader  # noqa: F401,

UserModel = get_user_model()


class BankAccountValidationError(ValidationError):
    pass


class BankAccountModelQuerySet(QuerySet):
    """
    A custom defined QuerySet for the BankAccountModel.
    """

    def active(self) -> QuerySet:  # noqa: C901
        """
        Active bank accounts which can be used to create new transactions.

        Returns
        _______
        BankAccountModelQuerySet
            A filtered BankAccountModelQuerySet of active accounts.
        """
        return self.filter(active=True)

    def hidden(self) -> QuerySet:  # noqa: C901
        """
        Hidden bank accounts which can be used to create new transactions. but will not show in   # noqa: E713drop down menus  # noqa: E501
        in the UI.

        Returns
        _______
        BankAccountModelQuerySet
            A filtered BankAccountModelQuerySet of active accounts.
        """
        return self.filter(hidden=True)


class BankAccountModelManager(models.Manager):
    """
    Custom defined Model Manager for the BankAccountModel.
    """

    def get_queryset(self) -> BankAccountModelQuerySet:  # noqa: C901
        return BankAccountModelQuerySet(self.model, using=self._db)

    def for_user(self, user_model):  # noqa: C901
        qs = self.get_queryset()
        if user_model.is_superuser:
            return qs
        return qs.filter(
            Q(entity_model__admin=user_model)
            | Q(entity_model__managers__in=[user_model])
        )

    def for_entity(
        self, entity_slug, user_model
    ) -> BankAccountModelQuerySet:  # noqa: C901
        """
        Allows only the authorized user to query the BankAccountModel for a given EntityModel.  # noqa: E501
        This is the recommended initial QuerySet.

        Parameters
        __________
        entity_slug: str or EntityModel
            The entity slug or EntityModel used for filtering the QuerySet.
        user_model
            Logged in and authenticated django UserModel instance.
        """
        qs = self.for_user(user_model)
        if isinstance(entity_slug, lazy_loader.get_entity_model()):
            return qs.filter(Q(entity_model=entity_slug))
        return qs.filter(Q(entity_model__slug__exact=entity_slug))


class BankAccountModelAbstract(FinancialAccountInfoMixin, CreateUpdateMixIn):
    """
    This is the main abstract class which the BankAccountModel database will inherit from.  # noqa: E501
    The BankAccountModel inherits functionality from the following MixIns:

        1. :func:`BankAccountInfoMixIn <django_ledger.models.mixins.BankAccountInfoMixIn>`  # noqa: E501
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`


    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    name: str
        A user defined name for the bank account as a String.
    entity_model: EntityModel
        The EntityModel associated with the BankAccountModel instance.
    account_model: AccountModel
        The AccountModel associated with the BankAccountModel instance. Must be an account with role ASSET_CA_CASH.  # noqa: E501
    active: bool
        Determines whether the BackAccountModel instance bank account is active. Defaults to True.  # noqa: E501
    hidden: bool
        Determines whether the BackAccountModel instance bank account is hidden. Defaults to False.  # noqa: E501
    action: str
        The action associated with this bank account.
    param: str
        Optional parameter for the bank account.
    unit_id: int
        ID of the unit associated with this bank account.
    ma_ngan_hang: str
        Bank code.
    tknh: str
        Bank account code.
    chu_tk: str
        Account owner name.
    chi_nhanh: str
        Bank branch.
    tk: str
        Accounting account code.
    ten_tknh: str
        Short name of the bank account.
    sten_tknh: str
        Alternative short name of the bank account.
    tinh_thanh: str
        Province/City.
    phone: str
        Phone number.
    fax: str
        Fax number.
    ghi_chu: str
        Notes.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    # todo: rename to account_name?...
    name = models.CharField(
        max_length=150,
        null=True,
        blank=True,
        verbose_name=_("Tên tài khoản ngân hàng"),
        help_text=_("Tên chính thức của tài khoản ngân hàng"),
    )
    entity_model = models.ForeignKey(
        "django_ledger.EntityModel",
        on_delete=models.CASCADE,
        verbose_name=_("Entity Model"),
    )
    account_model = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.RESTRICT,
        help_text=_(
            "Account model be used to map transactions from financial institution"
        ),
        verbose_name=_("Associated Account Model"),
        related_name="bank_accounts",
    )
    active = models.BooleanField(
        default=False,
        verbose_name=_("Trạng thái hoạt động"),
        help_text=_(
            "Xác định xem tài khoản ngân hàng có đang hoạt động hay không"
        ),
    )
    hidden = models.BooleanField(default=False)
    # Các trường bổ sung
    action = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Hành động"),
        help_text=_("Hành động liên quan đến tài khoản ngân hàng"),
    )
    param = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tham số"),
        help_text=_("Tham số tùy chọn cho tài khoản ngân hàng"),
    )
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.CASCADE,
        verbose_name=_('Unit ID'),
        related_name='bank_accounts',
    )
    ma_ngan_hang = models.ForeignKey(
        "django_ledger.NganHangModel",
        on_delete=models.RESTRICT,
        verbose_name=_("Ngân hàng"),
        help_text=_("Ngân hàng liên quan đến tài khoản ngân hàng"),
        related_name="bank_accounts",
        default=None,
        blank=True,
    )
    ma_tai_khoan = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Mã tài khoản"),
        help_text=_("Mã định danh của tài khoản ngân hàng"),
    )
    tknh = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Tài khoản ngân hàng"),
        help_text=_("Mã tài khoản ngân hàng"),
    )
    chu_tk = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Chủ tài khoản"),
        help_text=_("Tên chủ tài khoản"),
    )
    chi_nhanh = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Chi nhánh"),
        help_text=_("Chi nhánh ngân hàng"),
    )

    ten_tknh = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên viết tắt tài khoản ngân hàng"),
        help_text=_("Tên viết tắt của tài khoản ngân hàng"),
    )
    sten_tknh = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tên viết tắt tài khoản ngân hàng 2"),
        help_text=_("Tên viết tắt thứ 2 của tài khoản ngân hàng"),
    )
    tinh_thanh = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Tỉnh thành"),
        help_text=_("Tỉnh thành nơi đặt chi nhánh ngân hàng"),
    )
    phone = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Số điện thoại"),
        help_text=_("Số điện thoại liên hệ"),
    )
    fax = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name=_("Số fax"),
        help_text=_("Số fax liên hệ"),
    )
    ghi_chu = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name=_("Ghi chú"),
        help_text=_("Ghi chú bổ sung về tài khoản ngân hàng"),
    )

    objects = BankAccountModelManager()

    def configure(  # noqa: C901
        self,
        entity_slug,
        user_model: Optional[UserModel],
        commit: bool = False,
    ):

        EntityModel = lazy_loader.get_entity_model()
        if isinstance(entity_slug, str):
            if not user_model:
                raise BankAccountValidationError(
                    _("Must pass user_model when using entity_slug.")
                )
            entity_model_qs = EntityModel.objects.for_user(
                user_model=user_model
            )
            entity_model = get_object_or_404(
                entity_model_qs, slug__exact=entity_slug
            )
        elif isinstance(entity_slug, EntityModel):
            entity_model = entity_slug
        else:
            raise BankAccountValidationError(
                "entity_slug must be an instance of str or EntityModel"
            )

        self.entity_model = entity_model
        self.clean()
        if commit:
            self.save(update_fields=["entity_model", "updated"])
        return self, entity_model

    def is_active(self):  # noqa: C901
        return self.active is True

    class Meta:
        abstract = True
        verbose_name = _("Bank Account")
        indexes = [
            models.Index(fields=["account_type"]),
            models.Index(fields=["account_model"]),
        ]
        unique_together = [
            ("entity_model", "account_number"),
            (
                "entity_model",
                "account_model",
                "account_number",
                "routing_number",
            ),
        ]

    def __str__(self):  # noqa: C901
        if self.chu_tk and self.account_number:
            return f"{self.chu_tk} - {self.account_number}"
        elif self.name and self.account_number:
            return f"{self.name} - {self.account_number}"
        return f"Bank Account: {self.name}"

    def can_activate(self) -> bool:  # noqa: C901
        return self.active is False

    def can_inactivate(self) -> bool:  # noqa: C901
        return self.active is True

    def mark_as_active(
        self, commit: bool = False, raise_exception: bool = True, **kwargs
    ):  # noqa: C901
        if not self.can_activate():
            if raise_exception:
                raise BankAccountValidationError(
                    "Bank Account cannot be activated."
                )
        self.active = True
        if commit:
            self.save(update_fields=["active", "updated"])

    def mark_as_inactive(
        self, commit: bool = False, raise_exception: bool = True, **kwargs
    ):  # noqa: C901
        if not self.can_inactivate():
            if raise_exception:
                raise BankAccountValidationError(
                    "Bank Account cannot be deactivated."
                )
        self.active = False
        if commit:
            self.save(update_fields=["active", "updated"])


class BankAccountModel(BankAccountModelAbstract):
    """
    Base Bank Account Model Implementation
    """

    class Meta(BankAccountModelAbstract.Meta):
        # swappable ='DJANGO_LEDGER_BANK_ACCOUNT_MODEL'
        abstract = False
