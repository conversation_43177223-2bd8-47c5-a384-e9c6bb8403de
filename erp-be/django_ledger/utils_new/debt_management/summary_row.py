"""
Summary Row Utils for ERP Debt Management

Provides reusable summary row logic for debt/balance reports.
Used by both <PERSON> and <PERSON> <PERSON> Tiet Cong No APIs.
"""

from typing import Dict, Any, List


class SummaryRowUtils:
    """
    Utility class for creating and managing summary rows in debt reports.
    
    Summary row characteristics:
    - stt = 0 (always first)
    - bac_ct = 0 (summary level)
    - ten_kh = "Tổng cộng" (summary label)
    - Empty fields: tk, ma_kh
    - Calculated totals: no_dk, co_dk, ps_no, ps_co, no_ck, co_ck
    """

    # Standard summary row field names
    SUMMARY_FIELDS = ['no_dk', 'co_dk', 'ps_no', 'ps_co', 'no_ck', 'co_ck']
    
    @staticmethod
    def calculate_totals(data_rows: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Calculate totals from data rows for summary.
        
        Parameters
        ----------
        data_rows : List[Dict[str, Any]]
            List of customer/account records
            
        Returns
        -------
        Dict[str, float]
            Calculated totals for each summary field
        """
        totals = {}
        
        for field in SummaryRowUtils.SUMMARY_FIELDS:
            totals[field] = sum(
                float(record.get(field, 0.0)) 
                for record in data_rows
            )
        
        return totals

    @staticmethod
    def create_summary_row(
        data_rows: List[Dict[str, Any]], 
        additional_fields: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create summary row with calculated totals.
        
        Parameters
        ----------
        data_rows : List[Dict[str, Any]]
            List of data records to summarize
        additional_fields : Dict[str, Any], optional
            Additional fields to include in summary row
            
        Returns
        -------
        Dict[str, Any]
            Complete summary row record
        """
        # Calculate totals
        totals = SummaryRowUtils.calculate_totals(data_rows)
        
        # Create base summary row
        summary_row = {
            'stt': 0,
            'tk': '',
            'ma_kh': '',
            'ten_kh': 'Tổng cộng',
            'bac_ct': 0
        }
        
        # Add calculated totals
        summary_row.update(totals)
        
        # Add any additional fields
        if additional_fields:
            summary_row.update(additional_fields)
        
        return summary_row

    @staticmethod
    def create_empty_summary_row(additional_fields: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Create summary row with all zeros (for empty data scenarios).
        
        Parameters
        ----------
        additional_fields : Dict[str, Any], optional
            Additional fields to include
            
        Returns
        -------
        Dict[str, Any]
            Empty summary row with zeros
        """
        summary_row = {
            'stt': 0,
            'tk': '',
            'ma_kh': '',
            'ten_kh': 'Tổng cộng',
            'bac_ct': 0
        }
        
        # Add zero totals
        for field in SummaryRowUtils.SUMMARY_FIELDS:
            summary_row[field] = 0.0
        
        # Add any additional fields
        if additional_fields:
            summary_row.update(additional_fields)
        
        return summary_row

    @staticmethod
    def add_summary_to_report(data_rows: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Add summary row to the beginning of report data.
        
        Parameters
        ----------
        data_rows : List[Dict[str, Any]]
            Original data rows
            
        Returns
        -------
        List[Dict[str, Any]]
            Report data with summary row first
        """
        if not data_rows:
            # Empty data - return only summary row with zeros
            return [SummaryRowUtils.create_empty_summary_row()]
        
        # Calculate and create summary row
        summary_row = SummaryRowUtils.create_summary_row(data_rows)
        
        # Return summary first, then data
        return [summary_row] + data_rows

    @staticmethod
    def validate_summary_row(summary_row: Dict[str, Any]) -> bool:
        """
        Validate summary row structure and values.
        
        Parameters
        ----------
        summary_row : Dict[str, Any]
            Summary row to validate
            
        Returns
        -------
        bool
            True if valid summary row
        """
        # Check required fields
        required_fields = ['stt', 'tk', 'ma_kh', 'ten_kh', 'bac_ct'] + SummaryRowUtils.SUMMARY_FIELDS
        
        for field in required_fields:
            if field not in summary_row:
                return False
        
        # Check summary row characteristics
        if (summary_row['stt'] != 0 or 
            summary_row['bac_ct'] != 0 or 
            summary_row['ten_kh'] != 'Tổng cộng'):
            return False
        
        # Check that numeric fields are numbers
        for field in SummaryRowUtils.SUMMARY_FIELDS:
            try:
                float(summary_row[field])
            except (ValueError, TypeError):
                return False
        
        return True

    @staticmethod
    def extract_summary_from_report(
        report_data: List[Dict[str, Any]]
    ) -> tuple[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Extract summary row from report data if present.
        
        Parameters
        ----------
        report_data : List[Dict[str, Any]]
            Report data that may contain summary row
            
        Returns
        -------
        tuple[Dict[str, Any], List[Dict[str, Any]]]
            (summary_row, data_rows) - summary_row is None if not found
        """
        if not report_data:
            return None, []
        
        first_row = report_data[0]
        
        # Check if first row is summary row
        if (first_row.get('stt') == 0 and 
            first_row.get('ten_kh') == 'Tổng cộng'):
            return first_row, report_data[1:]
        
        return None, report_data

    @staticmethod
    def get_summary_statistics(summary_row: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get statistics from summary row for reporting.
        
        Parameters
        ----------
        summary_row : Dict[str, Any]
            Summary row data
            
        Returns
        -------
        Dict[str, Any]
            Summary statistics
        """
        if not SummaryRowUtils.validate_summary_row(summary_row):
            return {}
        
        stats = {
            'total_opening_debit': summary_row.get('no_dk', 0.0),
            'total_opening_credit': summary_row.get('co_dk', 0.0),
            'total_period_debit': summary_row.get('ps_no', 0.0),
            'total_period_credit': summary_row.get('ps_co', 0.0),
            'total_closing_debit': summary_row.get('no_ck', 0.0),
            'total_closing_credit': summary_row.get('co_ck', 0.0),
        }
        
        # Calculate net balances
        stats['net_opening_balance'] = stats['total_opening_debit'] - stats['total_opening_credit']
        stats['net_period_movement'] = stats['total_period_debit'] - stats['total_period_credit']
        stats['net_closing_balance'] = stats['total_closing_debit'] - stats['total_closing_credit']
        
        return stats
