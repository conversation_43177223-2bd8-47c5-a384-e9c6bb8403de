"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuThuChiTiet model.
"""

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.account import AccountModelSerializer  # noqa: F401
from django_ledger.api.serializers.contract import (  # noqa: F401,
    ContractModelSerializer,
)
from django_ledger.api.serializers.customer import (  # noqa: F401,
    CustomerModelSerializer,
)
from django_ledger.api.serializers.danh_muc import (  # noqa: F401,
    ChiPhiKhongHopLeSerializer,
    PhiSerializer,
)
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc import (  # noqa: F401,
    KheUocModelSerializer,
)
from django_ledger.api.serializers.organization import (  # noqa: F401,
    BoPhanModelSerializer,
)
from django_ledger.api.serializers.tien_do_thanh_toan import (  # noqa: F401,
    DotThanhToanModelSerializer,
)
from django_ledger.api.serializers.vat_tu import VatTuSerializer  # noqa: F401,
from django_ledger.api.serializers.vu_viec import VuViecModelSerializer  # noqa: F401,
from django_ledger.models import PhieuThuChiTietModel  # noqa: F401,


class PhieuThuChiTietSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuThuChiTiet model.
    """

    # Read-only fields for related objects
    phieu_thu_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    id_hd_data = serializers.SerializerMethodField(read_only=True)
    tk_co_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_hd_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_sp_data = serializers.SerializerMethodField(read_only=True)
    ma_cp0_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PhieuThuChiTietModel
        fields = [
            "uuid",
            "phieu_thu",
            "phieu_thu_data",
            "id",
            "line",
            "dien_giai",
            "ma_kh",
            "ma_kh_data",
            "id_hd",
            "id_hd_data",
            "tk_co",
            "tk_co_data",
            "ty_gia_hd",
            "ty_gia2",
            "tien_nt",
            "tien",
            "ma_bp",
            "ma_bp_data",
            "ma_vv",
            "ma_vv_data",
            "ma_hd",
            "ma_hd_data",
            "ma_dtt",
            "ma_dtt_data",
            "ma_ku",
            "ma_ku_data",
            "ma_phi",
            "ma_phi_data",
            "ma_sp",
            "ma_sp_data",
            "ma_lsx",
            "ma_cp0",
            "ma_cp0_data",
            "id_tt",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "phieu_thu_data",
            "ma_kh_data",
            "id_hd_data",
            "tk_co_data",
            "ma_bp_data",
            "ma_vv_data",
            "ma_hd_data",
            "ma_dtt_data",
            "ma_ku_data",
            "ma_phi_data",
            "ma_sp_data",
            "ma_cp0_data",
            "created",
            "updated",
        ]

    def get_phieu_thu_data(self, obj):  # noqa: C901
        """
        Get parent data.
        """
        if obj.phieu_thu:
            # Return basic fields without using PhieuThuSerializer to avoid circular import
            return {
                "uuid": str(obj.phieu_thu.uuid),
                "i_so_ct": obj.phieu_thu.i_so_ct,
                "dien_giai": obj.phieu_thu.dien_giai,
            }
        return None

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_id_hd_data(self, obj):  # noqa: C901
        """
        Get contract data.
        """
        if obj.id_hd:
            return ContractModelSerializer(obj.id_hd).data
        return None

    def get_tk_co_data(self, obj):  # noqa: C901
        """
        Get account data.
        """
        if obj.tk_co:
            return AccountModelSerializer(obj.tk_co).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanModelSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Get case data.
        """
        if obj.ma_vv:
            return VuViecModelSerializer(obj.ma_vv).data
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Get contract data.
        """
        if obj.ma_hd:
            return ContractModelSerializer(obj.ma_hd).data
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """
        Get payment batch data.
        """
        if obj.ma_dtt:
            return DotThanhToanModelSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Get loan agreement data.
        """
        if obj.ma_ku:
            return KheUocModelSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Get product data.
        """
        if obj.ma_sp:
            return VatTuSerializer(obj.ma_sp).data
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """
        Get invalid expense data.
        """
        if obj.ma_cp0:
            return ChiPhiKhongHopLeSerializer(obj.ma_cp0).data
        return None
