"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for PhieuThu model.
"""

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.account import AccountModelSerializer  # noqa: F401
from django_ledger.api.serializers.chung_tu import ChungTuSerializer  # noqa: F401,
from django_ledger.api.serializers.customer import (  # noqa: F401,
    CustomerModelSerializer,
)
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer  # noqa: F401,
from django_ledger.api.serializers.entity import EntityModelSerializer  # noqa: F401,
from django_ledger.api.serializers.han_thanh_toan import (  # noqa: F401,
    HanThanhToanModelSerializer,
)
from django_ledger.api.serializers.quyen_chung_tu import (  # noqa: F401,
    <PERSON>uy<PERSON><PERSON>hungTuDetailSerializer,
)
from django_ledger.api.serializers.tien_mat.hach_toan.phieu_thu.phieu_thu_chi_tiet import (  # noqa: F401,
    PhieuThuChiTietSerializer,
)
from django_ledger.api.serializers.unit import EntityUnitModelSerializer  # noqa: F401,
from django_ledger.models import PhieuThuModel  # noqa: F401,


class PhieuThuSerializer(serializers.ModelSerializer):
    """
    Serializer for PhieuThu model.
    """

    # Read-only fields for related objects
    tk_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    ma_nk_data = serializers.SerializerMethodField(read_only=True)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)

    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    # Child data
    child_data = serializers.SerializerMethodField(read_only=True)
    child_items = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = PhieuThuModel
        fields = [
            "uuid",
            "entity_model",
            "id",
            "ma_ngv",
            "dia_chi",
            "ong_ba",
            "dien_giai",
            "tk",
            "tk_data",
            "unit_id",
            "unit_id_data",
            "i_so_ct",
            "ma_nk",
            "ma_nk_data",
            "so_ct",
            "chung_tu",
            "ngay_ct",
            "ngay_lct",
            "ma_nt",
            "ma_nt_data",
            "ty_gia",
            "status",
            "so_ct0",
            "ngay_ct0",
            "so_ct_goc",
            "dien_giai_ct_goc",
            "ma_tt",
            "ma_tt_data",
            "ma_kh",
            "ma_kh_data",
            "t_tien_nt",
            "t_tien",
            "child_data",
            "child_items",
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "entity_model",
            "tk_data",
            "unit_id_data",
            "ma_nk_data",
            "ma_nt_data",
            "ma_tt_data",
            "ma_kh_data",
            "child_data",
            "t_tien_nt",
            "t_tien",
            "created",
            "updated",
        ]

    def get_tk_data(self, obj):  # noqa: C901
        """
        Get account data.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """
        Get unit data.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_ma_nk_data(self, obj):  # noqa: C901
        """
        Get document type data.
        """
        if obj.ma_nk:
            return QuyenChungTuDetailSerializer(obj.ma_nk).data
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        """
        Get payment term data.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_child_data(self, obj):  # noqa: C901
        """
        Get child details data.
        """
        children = obj.children.all()
        return PhieuThuChiTietSerializer(children, many=True).data

    def validate_ngay_ct(self, value):
        """
        Validate and convert ngay_ct to date object.
        """
        if isinstance(value, str):
            from datetime import datetime

            try:
                return datetime.strptime(value, '%Y-%m-%d').date()
            except ValueError:
                raise serializers.ValidationError(
                    "Ngày chứng từ phải có định dạng YYYY-MM-DD"
                )
        return value

    def validate_ngay_lct(self, value):
        """
        Validate and convert ngay_lct to date object.
        """
        if isinstance(value, str):
            from datetime import datetime

            try:
                return datetime.strptime(value, '%Y-%m-%d').date()
            except ValueError:
                raise serializers.ValidationError(
                    "Ngày lập chứng từ phải có định dạng YYYY-MM-DD"
                )
        return value

    def validate_ngay_ct0(self, value):
        """
        Validate and convert ngay_ct0 to date object.
        """
        if value and isinstance(value, str):
            from datetime import datetime

            try:
                return datetime.strptime(value, '%Y-%m-%d').date()
            except ValueError:
                raise serializers.ValidationError(
                    "Ngày chứng từ gốc phải có định dạng YYYY-MM-DD"
                )
        return value
