"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.accounts import (  # noqa: F401
    AccountModelSerializer,
)
from django_ledger.api.serializers.ngan_hang import (  # noqa: F401
    NganHangModelSerializer,
)
from django_ledger.api.serializers.unit import (  # noqa: F401
    EntityUnitModelSimpleSerializer,
)
from django_ledger.models import BankAccountModel  # noqa: F401,


class BankAccountModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the BankAccountModel

    This serializer handles the conversion between BankAccountModel instances and JSON representations,  # noqa: E501
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields (account_model, tk, etc.)  # noqa: E501
    - Adds additional fields with "_data" suffix (account_model_data, tk_data, etc.)
      that contain the complete nested object data with all available fields
    - When deserializing, accepts UUIDs for reference fields

    Example response:
    {
        "uuid": "dd06235c-76f3-44e1-b7f7-cb2be5e43639",
        "name": "Main Bank Account",
        "account_model": "815dde91-3f8a-4a27-8316-9e40859780ae",
        "account_model_data": {
            "uuid": "815dde91-3f8a-4a27-8316-9e40859780ae",
            "code": "1001",
            "name": "Cash Account",
            ...
        },
        ...
    }
    """

    # Legacy fields for backward compatibility
    account_name = serializers.CharField(
        source='account_model.name', read_only=True
    )
    account_code = serializers.CharField(
        source='account_model.code', read_only=True
    )
    # Define additional fields for nested data
    account_model_data = serializers.SerializerMethodField(read_only=True)
    ma_ngan_hang_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = BankAccountModel
        fields = [
            'uuid',
            'entity_model',
            'name',
            'account_type',
            'account_model',
            'account_model_data',
            'account_name',
            'account_code',
            'account_number',
            'routing_number',
            'aba_number',
            'active',
            'hidden',
            # Additional fields
            'action',
            'param',
            'unit_id',
            'unit_id_data',
            'ma_ngan_hang',
            'ma_ngan_hang_data',
            'tknh',
            'chu_tk',
            'chi_nhanh',
            'ten_tknh',
            'sten_tknh',
            'tinh_thanh',
            'phone',
            'fax',
            'ghi_chu',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'created',
            'updated',
            'account_model_data',
            'ma_ngan_hang_data',
            'unit_id_data',
        ]
        extra_kwargs = {'account_model': {'required': True}}

    def get_account_model_data(self, obj):  # noqa: C901
        """Method field for account_model_data"""
        if obj.account_model:
            return AccountModelSerializer(obj.account_model).data
        return None

    def get_ma_ngan_hang_data(self, obj):  # noqa: C901
        """Method field for ma_ngan_hang_data"""
        # ma_ngan_hang is now a foreign key to NganHangModel
        if obj.ma_ngan_hang:
            return NganHangModelSerializer(obj.ma_ngan_hang).data
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """Method field for unit_id_data"""
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def validate_chu_tk(self, value):  # noqa: C901
        """
        Validate chu_tk field
        """
        if value and len(value) > 255:
            raise serializers.ValidationError(
                'Tên chủ tài khoản không được vượt quá 255 ký tự'
            )
        return value

    def validate_ma_ngan_hang(self, value):  # noqa: C901
        """
        Validate ma_ngan_hang field
        """
        if value and len(value) > 50:
            raise serializers.ValidationError(
                'Mã ngân hàng không được vượt quá 50 ký tự'
            )
        return value
