"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bang Can Doi Phat Sinh Cong No (Customer Debt Balance Report) API.
"""

from rest_framework import serializers
from decimal import Decimal
from datetime import date, datetime


class BangCanDoiPhatSinhCongNoRequestSerializer(serializers.Serializer):
    """
    Serializer for Bang Can Doi Phat Sinh Cong No request parameters.
    Validates all filter parameters from the cURL request.
    """

    # Date range filters (required)
    ngay_ct1 = serializers.Char<PERSON>ield(
        required=True,
        max_length=8,
        help_text="Start date (YYYYMMDD format)"
    )
    ngay_ct2 = serializers.CharField(
        required=True,
        max_length=8,
        help_text="End date (YYYYMMDD format)"
    )

    # Account filter - Array of UUIDs
    tk = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        allow_empty=True,
        help_text="Account UUIDs array (e.g., ['uuid1','uuid2'])"
    )

    # View type filter
    kieu_xem = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=10,
        default="1",
        help_text="View type (1=standard view)"
    )

    # Transaction type filter
    ct_vt = serializers.IntegerField(
        required=False,
        default=0,
        help_text="Transaction type filter (0=all)"
    )

    # Customer filter - Array of UUIDs
    ma_kh = serializers.ListField(
        child=serializers.CharField(max_length=50),
        required=False,
        allow_empty=True,
        help_text="Customer UUIDs array (e.g., ['uuid1','uuid2'])"
    )

    # Customer hierarchy filters - UUID fields (updated)
    nh_kh1 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 1 UUID filter"
    )
    nh_kh2 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 2 UUID filter"
    )
    nh_kh3 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 3 UUID filter"
    )

    # Region filter - UUID field (updated)
    rg_code = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Region UUID filter"
    )

    # Grouping filter - String codes (comma-separated)
    group_by = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=200,
        help_text="Group codes (comma-separated, e.g., '230,220,210')"
    )

    # Unit filter
    ma_unit = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Unit code filter"
    )

    # Report template
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )

    # Test parameter for development
    test = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=10,
        help_text="Return real test data from script (use 'true' to enable)"
    )

    def validate_ngay_ct1(self, value):
        """
        Validate start date format.
        """
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "Start date must be in YYYYMMDD format"
            )

    def validate_ngay_ct2(self, value):
        """
        Validate end date format.
        """
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "End date must be in YYYYMMDD format"
            )

    def validate(self, data):
        """
        Validate the date range.
        """
        ngay_ct1 = data.get('ngay_ct1')
        ngay_ct2 = data.get('ngay_ct2')

        if ngay_ct1 and ngay_ct2:
            try:
                date1 = datetime.strptime(ngay_ct1, "%Y%m%d").date()
                date2 = datetime.strptime(ngay_ct2, "%Y%m%d").date()

                if date1 > date2:
                    raise serializers.ValidationError(
                        "Start date (ngay_ct1) must be less than or equal to end date (ngay_ct2)"
                    )
            except ValueError:
                pass  # Individual field validation will catch format errors

        return data


class BangCanDoiPhatSinhCongNoResponseSerializer(serializers.Serializer):
    """
    Serializer for Bang Can Doi Phat Sinh Cong No response data.
    Defines all fields that should be returned in the report based on user specification.
    """

    stt = serializers.IntegerField(help_text="Sequential number")
    tk = serializers.CharField(max_length=50, help_text="Account code")
    ma_kh = serializers.CharField(max_length=50, help_text="Customer code")
    no_dk = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Opening debit balance")
    co_dk = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Opening credit balance")
    ps_no = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Debit transactions")
    ps_co = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Credit transactions")
    nhom = serializers.CharField(max_length=50, help_text="Group level 1")
    nhom1 = serializers.CharField(max_length=50, help_text="Group level 2")
    nhom2 = serializers.CharField(max_length=50, help_text="Group level 3")
    nhom3 = serializers.CharField(max_length=50, help_text="Group level 4")
    no_ck = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Closing debit balance")
    co_ck = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Closing credit balance")
    ten_kh = serializers.CharField(max_length=255, help_text="Customer name")
