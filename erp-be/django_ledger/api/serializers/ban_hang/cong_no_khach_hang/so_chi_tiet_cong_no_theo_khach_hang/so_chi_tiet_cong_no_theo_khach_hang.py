"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for So Chi Tiet Cong No <PERSON> (Customer Debt Detail Report) API.
"""

from datetime import datetime
from rest_framework import serializers


class SoChiTietCongNoTheoKhachHangRequestSerializer(serializers.Serializer):
    """
    Serializer for So Chi Tiet Cong No Theo <PERSON>ch Hang request parameters.
    Validates all filter parameters from the cURL request.
    """

    # Date range filters (required)
    ngay_ct1 = serializers.Char<PERSON>ield(
        required=True,
        max_length=8,
        help_text="Start date (YYYYMMDD format)"
    )
    ngay_ct2 = serializers.CharField(
        required=True,
        max_length=8,
        help_text="End date (YYYYMMDD format)"
    )

    # Account filter - UUID only (always required)
    tk = serializers.Char<PERSON>ield(
        required=True,
        allow_blank=False,
        max_length=50,
        help_text="Account UUID (required, e.g., '6ba7b810-9dad-11d1-80b4-00c04fd430c8')"
    )

    # Customer filter - UUID only (always required)
    ma_kh = serializers.Char<PERSON><PERSON>(
        required=True,
        allow_blank=False,
        max_length=50,
        help_text="Customer UUID (required, e.g., '550e8400-e29b-41d4-a716-************')"
    )

    # Unit/Department filters
    ma_unit = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Unit code"
    )

    ma_bp = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Department code"
    )

    ma_vv = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Business area code"
    )

    # Additional filters from cURL (using IntegerField: 0=no details, 1=include product details)
    ct_vt = serializers.IntegerField(
        required=False,
        default=0,
        help_text="Include product details (0=no details, 1=include so_luong, gia2, tien2 fields)"
    )

    so_du = serializers.IntegerField(
        required=False,
        default=0,
        help_text="Include running balance (0=no balance, 1=include du_no, du_co fields)"
    )

    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template"
    )

    def validate_ngay_ct1(self, value):
        """
        Validate start date format.
        """
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "Invalid date format. Expected YYYYMMDD (e.g., 20240101)"
            )

    def validate_ngay_ct2(self, value):
        """
        Validate end date format.
        """
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "Invalid date format. Expected YYYYMMDD (e.g., 20241231)"
            )

    def validate(self, data):
        """
        Validate the date range.
        """
        ngay_ct1 = data.get('ngay_ct1')
        ngay_ct2 = data.get('ngay_ct2')

        if ngay_ct1 and ngay_ct2:
            try:
                date1 = datetime.strptime(ngay_ct1, "%Y%m%d").date()
                date2 = datetime.strptime(ngay_ct2, "%Y%m%d").date()

                if date1 > date2:
                    raise serializers.ValidationError(
                        "Start date (ngay_ct1) must be less than or equal to end date (ngay_ct2)"
                    )
            except ValueError:
                pass  # Individual field validation will catch format errors

        return data


class SoChiTietCongNoTheoKhachHangResponseSerializer(serializers.Serializer):
    """
    Serializer for So Chi Tiet Cong No Theo Khach Hang response data.
    Defines all fields that should be returned in the report based on user specification.
    """

    stt = serializers.IntegerField(help_text="Sequential number")
    tk = serializers.CharField(max_length=50, help_text="Account code")
    so_ct0 = serializers.CharField(max_length=50, help_text="Original document number")
    ngay_ct0 = serializers.CharField(max_length=8, help_text="Original document date (YYYYMMDD)")
    xorder = serializers.IntegerField(help_text="Order number")
    line = serializers.IntegerField(help_text="Line number")
    id = serializers.CharField(max_length=50, help_text="Transaction ID")
    unit_id = serializers.CharField(max_length=50, help_text="Unit ID")
    ma_ct = serializers.CharField(max_length=50, help_text="Document code")
    ngay_ct = serializers.CharField(max_length=8, help_text="Document date (YYYYMMDD)")
    so_ct = serializers.CharField(max_length=50, help_text="Document number")
    tk_du = serializers.CharField(max_length=50, help_text="Corresponding account")
    ps_no = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Debit amount")
    ps_co = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Credit amount")
    dien_giai = serializers.CharField(max_length=255, help_text="Description")
    ma_bp = serializers.CharField(max_length=50, help_text="Department code")
    ma_vv = serializers.CharField(max_length=50, help_text="Business area code")
    ma_unit = serializers.CharField(max_length=50, help_text="Unit code")

    def to_representation(self, instance):
        """
        Custom representation to conditionally include product detail fields.

        When ct_vt=true is sent in request, the service adds so_luong, gia2, tien2
        fields to all records. This method includes them in the response only
        when they exist in the source data.
        """
        # Get base representation
        data = super().to_representation(instance)

        # Conditionally add product detail fields if present in source data
        if 'so_luong' in instance:
            data['so_luong'] = float(instance.get('so_luong', 0.0))

        if 'gia2' in instance:
            data['gia2'] = float(instance.get('gia2', 0.0))

        if 'tien2' in instance:
            data['tien2'] = float(instance.get('tien2', 0.0))

        # Conditionally add running balance fields if present in source data
        if 'du_no' in instance:
            data['du_no'] = float(instance.get('du_no', 0.0))

        if 'du_co' in instance:
            data['du_co'] = float(instance.get('du_co', 0.0))

        return data
