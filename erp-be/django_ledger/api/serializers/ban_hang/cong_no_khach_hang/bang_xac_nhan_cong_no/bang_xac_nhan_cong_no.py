"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Bang Xac Nhan Cong No (Debt Confirmation Report) API.
"""

from datetime import datetime
from rest_framework import serializers


class BangXacNhanCongNoRequestSerializer(serializers.Serializer):
    """
    Serializer for Bang Xac Nhan Cong No request parameters.
    Validates all filter parameters from the cURL request.
    """

    # Date range filters (required) - từ cURL request
    ngay_ct1 = serializers.Char<PERSON>ield(
        required=True,
        max_length=8,
        help_text="Start date (YYYYMMDD format)"
    )
    ngay_ct2 = serializers.CharField(
        required=True,
        max_length=8,
        help_text="End date (YYYYMMDD format)"
    )

    # Account filter - UUID (required)
    tk = serializers.UUIDField(
        required=True,
        help_text="Account UUID"
    )

    # Invoice date range filters - từ cURL request
    ngay_hd1 = serializers.Char<PERSON>ield(
        required=False,
        max_length=8,
        allow_blank=True,
        help_text="Invoice start date (YYYYMMDD format)"
    )
    ngay_hd2 = serializers.Char<PERSON><PERSON>(
        required=False,
        max_length=8,
        allow_blank=True,
        help_text="Invoice end date (YYYYMMDD format)"
    )

    # Customer filter - UUID (required)
    ma_kh = serializers.UUIDField(
        required=True,
        help_text="Customer UUID"
    )

    # Balance filter - từ cURL request
    so_du = serializers.IntegerField(
        required=False,
        default=0,
        help_text="Balance filter (0=all, 1=with balance only)"
    )

    # Document type filter - từ cURL request
    ct_yn = serializers.IntegerField(
        required=False,
        default=1,
        help_text="Document type filter (0=all, 1=with documents only)"
    )

    # Unit filter - từ cURL request
    ma_unit = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Unit code filter"
    )

    # Report template - từ cURL request
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )

    # Creator filter - từ cURL request
    nguoi_lap = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Creator filter"
    )

    def validate_ngay_ct1(self, value):
        """
        Validate start date format.
        """
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "Start date must be in YYYYMMDD format"
            )

    def validate_ngay_ct2(self, value):
        """
        Validate end date format.
        """
        try:
            datetime.strptime(value, "%Y%m%d")
            return value
        except ValueError:
            raise serializers.ValidationError(
                "End date must be in YYYYMMDD format"
            )

    def validate_ngay_hd1(self, value):
        """
        Validate invoice start date format.
        """
        if value and value.strip():
            try:
                datetime.strptime(value, "%Y%m%d")
                return value
            except ValueError:
                raise serializers.ValidationError(
                    "Invoice start date must be in YYYYMMDD format"
                )
        return value

    def validate_ngay_hd2(self, value):
        """
        Validate invoice end date format.
        """
        if value and value.strip():
            try:
                datetime.strptime(value, "%Y%m%d")
                return value
            except ValueError:
                raise serializers.ValidationError(
                    "Invoice end date must be in YYYYMMDD format"
                )
        return value

    def validate(self, data):
        """
        Validate the date ranges.
        """
        ngay_ct1 = data.get('ngay_ct1')
        ngay_ct2 = data.get('ngay_ct2')

        if ngay_ct1 and ngay_ct2:
            try:
                date1 = datetime.strptime(ngay_ct1, "%Y%m%d").date()
                date2 = datetime.strptime(ngay_ct2, "%Y%m%d").date()

                if date1 > date2:
                    raise serializers.ValidationError(
                        "Start date (ngay_ct1) must be less than or equal to end date (ngay_ct2)"
                    )
            except ValueError:
                pass  # Individual field validation will catch format errors

        # Validate invoice date range if provided
        ngay_hd1 = data.get('ngay_hd1')
        ngay_hd2 = data.get('ngay_hd2')

        if ngay_hd1 and ngay_hd2 and ngay_hd1.strip() and ngay_hd2.strip():
            try:
                hd_date1 = datetime.strptime(ngay_hd1, "%Y%m%d").date()
                hd_date2 = datetime.strptime(ngay_hd2, "%Y%m%d").date()

                if hd_date1 > hd_date2:
                    raise serializers.ValidationError(
                        "Invoice start date (ngay_hd1) must be less than or equal to invoice end date (ngay_hd2)"
                    )
            except ValueError:
                pass  # Individual field validation will catch format errors

        return data


class BangXacNhanCongNoResponseSerializer(serializers.Serializer):
    """
    Serializer for Bang Xac Nhan Cong No response data.
    Defines all 17 base fields + 2 optional running balance fields (du_no, du_co) when so_du=true.
    """

    stt = serializers.IntegerField(help_text="Sequential number")
    tk = serializers.CharField(max_length=50, help_text="Account code")
    id = serializers.CharField(max_length=50, help_text="Record ID")
    unit_id = serializers.CharField(max_length=50, help_text="Unit ID")
    ma_ct = serializers.CharField(max_length=50, help_text="Document type code")
    ngay_ct = serializers.CharField(max_length=50, help_text="Document date")
    so_ct = serializers.CharField(max_length=50, help_text="Document number")
    ma_kh = serializers.CharField(max_length=50, help_text="Customer code")
    tk_du = serializers.CharField(max_length=50, help_text="Corresponding account")
    ps_no = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Debit amount")
    ps_co = serializers.DecimalField(max_digits=15, decimal_places=2, help_text="Credit amount")
    dien_giai = serializers.CharField(max_length=255, help_text="Description")
    ma_bp = serializers.CharField(max_length=50, help_text="Department code")
    ma_vv = serializers.CharField(max_length=50, help_text="Purpose code")
    ma_unit = serializers.CharField(max_length=50, help_text="Unit code")
    so_dh = serializers.CharField(max_length=50, help_text="Order number")
    id_dh = serializers.CharField(max_length=50, help_text="Order ID")

    # Running balance fields (optional - only present when so_du=true)
    du_no = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        required=False,
        help_text="Running debit balance (when so_du=true)"
    )
    du_co = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        required=False,
        help_text="Running credit balance (when so_du=true)"
    )
