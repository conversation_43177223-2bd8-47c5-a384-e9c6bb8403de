"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for So Chi Tiet Cong No Nhieu Khach Hang API.
"""

from rest_framework import serializers
from typing import Dict, Any, List


class SoChiTietCongNoNhieuKhachHangRequestSerializer(serializers.Serializer):
    """
    Serializer for So Chi Tiet Cong No Nhieu Khach Hang API request/response.

    Handles validation of filter parameters from cURL request and
    formats response data according to specification.

    Key differences from Bang Can Doi Phat Sinh Cong No:
    - tk (account): REQUIRED, single account only (not array)
    - ma_kh (customer): Optional, if empty = all customers, if specified = only that customer
    - Uses same calculation logic (type 1, 2, 3) as Bang Can Doi
    """

    # Request fields (from cURL data)
    ngay_ct1 = serializers.CharField(
        required=False,
        default='********',
        help_text="Start date in YYYYMMDD format"
    )
    ngay_ct2 = serializers.CharField(
        required=False,
        default='********',
        help_text="End date in YYYYMMDD format"
    )
    tk = serializers.UUIDField(
        required=True,
        help_text="Account UUID (REQUIRED - single account only)"
    )
    ct_vt = serializers.IntegerField(
        required=False,
        default=0,
        help_text="Product detail flag (0=no, 1=yes)"
    )
    ma_kh = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer UUID filter (empty = all customers)"
    )
    tk_cn = serializers.IntegerField(
        required=False,
        default=1,
        help_text="Account branch flag"
    )
    so_du = serializers.IntegerField(
        required=False,
        default=0,
        help_text="Running balance flag (0=no, 1=yes)"
    )
    nh_kh1 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 1 UUID filter"
    )
    nh_kh2 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 2 UUID filter"
    )
    nh_kh3 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 3 UUID filter"
    )
    rg_code = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Region UUID filter"
    )
    nguoi_lap = serializers.CharField(
        required=False,
        default='',
        allow_blank=True,
        help_text="Creator filter"
    )
    ma_unit = serializers.CharField(
        required=False,
        default='',
        allow_blank=True,
        help_text="Unit code filter"
    )
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )

    def validate_ngay_ct1(self, value: str) -> str:
        """Validate start date format."""
        if not value or len(value) != 8:
            raise serializers.ValidationError("Start date must be in YYYYMMDD format")

        try:
            from datetime import datetime
            datetime.strptime(value, '%Y%m%d')
        except ValueError:
            raise serializers.ValidationError("Invalid start date format")

        return value

    def validate_ngay_ct2(self, value: str) -> str:
        """Validate end date format."""
        if not value or len(value) != 8:
            raise serializers.ValidationError("End date must be in YYYYMMDD format")

        try:
            from datetime import datetime
            datetime.strptime(value, '%Y%m%d')
        except ValueError:
            raise serializers.ValidationError("Invalid end date format")

        return value



    def validate_ma_kh(self, value: str) -> str:
        """Validate customer UUID - OPTIONAL field."""
        if not value or not value.strip():
            return ''  # Empty is allowed (means all customers)

        # Validate UUID format if provided
        import uuid
        try:
            uuid.UUID(value.strip())
        except (ValueError, TypeError):
            raise serializers.ValidationError("Customer field must be a valid UUID")

        return value.strip()

    def validate_ct_vt(self, value: int) -> int:
        """Validate product detail flag."""
        if value not in [0, 1]:
            raise serializers.ValidationError("Product detail flag must be 0 or 1")
        return value

    def validate_so_du(self, value: int) -> int:
        """Validate running balance flag."""
        if value not in [0, 1]:
            raise serializers.ValidationError("Running balance flag must be 0 or 1")
        return value

    def validate_tk_cn(self, value: int) -> int:
        """Validate account branch flag."""
        if value not in [0, 1]:
            raise serializers.ValidationError("Account branch flag must be 0 or 1")
        return value

    def validate_mau_bc(self, value: int) -> int:
        """Validate report template number."""
        if value < 1:
            raise serializers.ValidationError("Report template number must be positive")
        return value

    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """Cross-field validation."""
        from datetime import datetime

        # Validate that account UUID is provided (required field)
        tk = attrs.get('tk')
        if not tk:
            raise serializers.ValidationError("Account UUID (tk) is required and cannot be empty")

        # Validate date range
        start_date_str = attrs.get('ngay_ct1', '********')
        end_date_str = attrs.get('ngay_ct2', '********')

        try:
            start_date = datetime.strptime(start_date_str, '%Y%m%d').date()
            end_date = datetime.strptime(end_date_str, '%Y%m%d').date()

            if start_date > end_date:
                raise serializers.ValidationError("Start date must be before or equal to end date")

        except ValueError:
            raise serializers.ValidationError("Invalid date format")

        return attrs


class SoChiTietCongNoNhieuKhachHangResponseSerializer(serializers.Serializer):
    """
    Response serializer for So Chi Tiet Cong No Nhieu Khach Hang API.

    Defines the exact response format matching cURL specification.
    """

    stt = serializers.IntegerField(help_text="Sequential number")
    tk = serializers.CharField(help_text="Account code")
    ma_kh = serializers.CharField(help_text="Customer code")
    no_dk = serializers.FloatField(help_text="Opening debit balance")
    co_dk = serializers.FloatField(help_text="Opening credit balance")
    ps_no = serializers.FloatField(help_text="Period debit movements")
    ps_co = serializers.FloatField(help_text="Period credit movements")
    no_ck = serializers.FloatField(help_text="Closing debit balance")
    co_ck = serializers.FloatField(help_text="Closing credit balance")
    bac_ct = serializers.IntegerField(help_text="Document level")
    ten_kh = serializers.CharField(help_text="Customer name")

    class Meta:
        """Meta configuration for response serializer."""
        fields = [
            'stt', 'tk', 'ma_kh', 'no_dk', 'co_dk',
            'ps_no', 'ps_co', 'no_ck', 'co_ck',
            'bac_ct', 'ten_kh'
        ]


class SoChiTietCongNoNhieuKhachHangAPIResponseSerializer(serializers.Serializer):
    """
    Complete API response serializer including metadata.
    """

    success = serializers.BooleanField(default=True)
    message = serializers.CharField(default="Report generated successfully")
    count = serializers.IntegerField(help_text="Number of records returned")
    data = SoChiTietCongNoNhieuKhachHangResponseSerializer(many=True)

    # Optional metadata
    filters_applied = serializers.DictField(required=False)
    execution_time_ms = serializers.FloatField(required=False)

    class Meta:
        """Meta configuration for API response."""
        fields = [
            'success', 'message', 'count', 'data',
            'filters_applied', 'execution_time_ms'
        ]


# Backward compatibility aliases
SoChiTietCongNoNhieuKhachHangSerializer = SoChiTietCongNoNhieuKhachHangRequestSerializer