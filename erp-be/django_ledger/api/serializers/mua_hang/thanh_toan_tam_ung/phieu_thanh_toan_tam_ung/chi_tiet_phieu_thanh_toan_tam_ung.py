"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuThanhToanTamUng (Advance Payment Settlement Voucher Detail) Serializer
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.models.mua_hang.thanh_toan_tam_ung import (  # noqa: F401
    ChiTietPhieuThanhToanTamUngModel,
)
from django_ledger.services.mua_hang.thanh_toan_tam_ung import (  # noqa: F401,
    ChiTietPhieuThanhToanTamUngService,
)


class ChiTietPhieuThanhToanTamUngSerializer(serializers.ModelSerializer):
    """
    A serializer class for the ChiTietPhieuThanhToanTamUngModel.
    """

    _data = serializers.SerializerMethodField()
    phieu_thanh_toan_data = serializers.SerializerMethodField()
    tk_no_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()
    tk_thue_data = serializers.SerializerMethodField()
    ma_kh9_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiTietPhieuThanhToanTamUngModel
        fields = [
            'uuid',
            'phieu_thanh_toan',
            'phieu_thanh_toan_data',
            'line',
            'tk_no',
            'tk_no_data',
            'ma_kh',
            'ma_kh_data',
            'tien_nt',
            'tien',
            'dien_giai',
            'ma_loai_hd',
            'ma_thue',
            'ten_thue',
            'thue_suat',
            'tk_thue',
            'tk_thue_data',
            'ten_tk_thue',
            'so_ct0',
            'so_ct2',
            'ngay_ct0',
            'ma_mau_ct',
            'ma_mau_bc',
            'ma_tc_thue',
            'ma_kh_thue',
            'ten_kh_thue',
            'dia_chi',
            'ma_so_thue',
            'ten_vt_thue',
            'thue_nt',
            'thue',
            'ma_kh9',
            'ma_kh9_data',
            'ten_kh9',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'id_tt',
            '_data',
        ]
        read_only_fields = ['uuid']

    def validate(self, data):
        """
        Custom validation to handle empty strings for ForeignKey fields.
        """
        # List of ForeignKey fields that can be null
        nullable_fk_fields = [
            'ma_vv', 'ma_dtt', 'ma_ku', 'ma_phi', 'ma_cp0'
        ]

        # Convert empty strings to None for nullable ForeignKey fields
        for field in nullable_fk_fields:
            if field in data and data[field] == "":
                data[field] = None

        return data

    def get__data(self, instance):  # noqa: C901
        """
        Get referenced data for the instance.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing referenced data.
        """
        data = {}
        if hasattr(instance, 'phieu_thanh_toan') and instance.phieu_thanh_toan:
            data['phieu_thanh_toan'] = {
                'uuid': instance.phieu_thanh_toan.uuid,
                'so_ct': instance.phieu_thanh_toan.so_ct,
                'dien_giai': instance.phieu_thanh_toan.dien_giai,
            }

        if hasattr(instance, 'tk_no') and instance.tk_no:
            data['tk_no'] = {
                'uuid': instance.tk_no.uuid,
                'code': instance.tk_no.code,
                'name': instance.tk_no.name,
            }

        if hasattr(instance, 'ma_kh') and instance.ma_kh:
            data['ma_kh'] = {
                'uuid': instance.ma_kh.uuid,
                'customer_code': instance.ma_kh.customer_code,
                'customer_name': instance.ma_kh.customer_name,
                'customer_number': instance.ma_kh.customer_number,
                'tax_code': instance.ma_kh.tax_code,
                'address': instance.ma_kh.address,
                'phone': instance.ma_kh.phone,
                'email': instance.ma_kh.email,
            }

        if hasattr(instance, 'tk_thue') and instance.tk_thue:
            data['tk_thue'] = {
                'uuid': instance.tk_thue.uuid,
                'code': instance.tk_thue.code,
                'name': instance.tk_thue.name,
            }

        if hasattr(instance, 'ma_kh9') and instance.ma_kh9:
            data['ma_kh9'] = {
                'uuid': instance.ma_kh9.uuid,
                'customer_code': instance.ma_kh9.customer_code,
                'customer_name': instance.ma_kh9.customer_name,
                'customer_number': instance.ma_kh9.customer_number,
                'tax_code': instance.ma_kh9.tax_code,
                'address': instance.ma_kh9.address,
                'phone': instance.ma_kh9.phone,
                'email': instance.ma_kh9.email,
            }

        if hasattr(instance, 'ma_bp') and instance.ma_bp:
            data['ma_bp'] = {
                'uuid': instance.ma_bp.uuid,
                'ma_bp': instance.ma_bp.ma_bp,
                'ten_bp': instance.ma_bp.ten_bp,
            }

        if hasattr(instance, 'ma_vv') and instance.ma_vv:
            data['ma_vv'] = {
                'uuid': instance.ma_vv.uuid,
                'ma_vv': instance.ma_vv.ma_vv,
                'ten_vv': instance.ma_vv.ten_vv,
            }

        if hasattr(instance, 'ma_hd') and instance.ma_hd:
            data['ma_hd'] = {
                'uuid': instance.ma_hd.uuid,
                'ma_hd': instance.ma_hd.ma_hd,
                'ten_hd': instance.ma_hd.ten_hd,
            }

        if hasattr(instance, 'ma_dtt') and instance.ma_dtt:
            data['ma_dtt'] = {
                'uuid': instance.ma_dtt.uuid,
                'ma_dtt': instance.ma_dtt.ma_dtt,
                'ten_dtt': instance.ma_dtt.ten_dtt,
            }

        if hasattr(instance, 'ma_ku') and instance.ma_ku:
            data['ma_ku'] = {
                'uuid': instance.ma_ku.uuid,
                'ma_ku': instance.ma_ku.ma_ku,
                'ten_ku': instance.ma_ku.ten_ku,
            }

        if hasattr(instance, 'ma_phi') and instance.ma_phi:
            data['ma_phi'] = {
                'uuid': instance.ma_phi.uuid,
                'ma_phi': instance.ma_phi.ma_phi,
                'ten_phi': instance.ma_phi.ten_phi,
            }

        if hasattr(instance, 'ma_sp') and instance.ma_sp:
            data['ma_sp'] = {
                'uuid': instance.ma_sp.uuid,
                'ma_vt': instance.ma_sp.ma_vt,
                'ten_vt': instance.ma_sp.ten_vt,
            }

        if hasattr(instance, 'ma_cp0') and instance.ma_cp0:
            data['ma_cp0'] = {
                'uuid': instance.ma_cp0.uuid,
                'ma_cp': instance.ma_cp0.ma_cp,
                'ten_cp': instance.ma_cp0.ten_cp,
            }

        return data

    def get_phieu_thanh_toan_data(self, instance):  # noqa: C901
        """
        Get data for the phieu_thanh_toan field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the phieu_thanh_toan data.
        """
        if hasattr(instance, 'phieu_thanh_toan') and instance.phieu_thanh_toan:
            return {
                'uuid': instance.phieu_thanh_toan.uuid,
                'so_ct': instance.phieu_thanh_toan.so_ct,
                'dien_giai': instance.phieu_thanh_toan.dien_giai,
            }
        return None

    def get_tk_no_data(self, instance):  # noqa: C901
        """
        Get data for the tk_no field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the tk_no data.
        """
        if hasattr(instance, 'tk_no') and instance.tk_no:
            return {
                'uuid': instance.tk_no.uuid,
                'code': instance.tk_no.code,
                'name': instance.tk_no.name,
            }
        return None

    def get_ma_kh_data(self, instance):  # noqa: C901
        """
        Get data for the ma_kh field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_kh data.
        """
        if hasattr(instance, 'ma_kh') and instance.ma_kh:
            return {
                'uuid': instance.ma_kh.uuid,
                'customer_code': instance.ma_kh.customer_code,
                'customer_name': instance.ma_kh.customer_name,
                'customer_number': instance.ma_kh.customer_number,
                'tax_code': instance.ma_kh.tax_code,
                'address': instance.ma_kh.address,
                'phone': instance.ma_kh.phone,
                'email': instance.ma_kh.email,
            }
        return None

    def get_tk_thue_data(self, instance):  # noqa: C901
        """
        Get data for the tk_thue field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the tk_thue data.
        """
        if hasattr(instance, 'tk_thue') and instance.tk_thue:
            return {
                'uuid': instance.tk_thue.uuid,
                'code': instance.tk_thue.code,
                'name': instance.tk_thue.name,
            }
        return None

    def get_ma_kh9_data(self, instance):  # noqa: C901
        """
        Get data for the ma_kh9 field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_kh9 data.
        """
        if hasattr(instance, 'ma_kh9') and instance.ma_kh9:
            return {
                'uuid': instance.ma_kh9.uuid,
                'customer_code': instance.ma_kh9.customer_code,
                'customer_name': instance.ma_kh9.customer_name,
                'customer_number': instance.ma_kh9.customer_number,
                'tax_code': instance.ma_kh9.tax_code,
                'address': instance.ma_kh9.address,
                'phone': instance.ma_kh9.phone,
                'email': instance.ma_kh9.email,
            }
        return None

    def get_ma_bp_data(self, instance):  # noqa: C901
        """
        Get data for the ma_bp field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_bp data.
        """
        if hasattr(instance, 'ma_bp') and instance.ma_bp:
            return {
                'uuid': instance.ma_bp.uuid,
                'ma_bp': instance.ma_bp.ma_bp,
                'ten_bp': instance.ma_bp.ten_bp,
            }
        return None

    def get_ma_vv_data(self, instance):  # noqa: C901
        """
        Get data for the ma_vv field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_vv data.
        """
        if hasattr(instance, 'ma_vv') and instance.ma_vv:
            return {
                'uuid': instance.ma_vv.uuid,
                'ma_vv': instance.ma_vv.ma_vv,
                'ten_vv': instance.ma_vv.ten_vv,
            }
        return None

    def get_ma_hd_data(self, instance):  # noqa: C901
        """
        Get data for the ma_hd field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_hd data.
        """
        if hasattr(instance, 'ma_hd') and instance.ma_hd:
            return {
                'uuid': instance.ma_hd.uuid,
                'ma_hd': instance.ma_hd.ma_hd,
                'ten_hd': instance.ma_hd.ten_hd,
            }
        return None

    def get_ma_dtt_data(self, instance):  # noqa: C901
        """
        Get data for the ma_dtt field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_dtt data.
        """
        if hasattr(instance, 'ma_dtt') and instance.ma_dtt:
            return {
                'uuid': instance.ma_dtt.uuid,
                'ma_dtt': instance.ma_dtt.ma_dtt,
                'ten_dtt': instance.ma_dtt.ten_dtt,
            }
        return None

    def get_ma_ku_data(self, instance):  # noqa: C901
        """
        Get data for the ma_ku field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_ku data.
        """
        if hasattr(instance, 'ma_ku') and instance.ma_ku:
            return {
                'uuid': instance.ma_ku.uuid,
                'ma_ku': instance.ma_ku.ma_ku,
                'ten_ku': instance.ma_ku.ten_ku,
            }
        return None

    def get_ma_phi_data(self, instance):  # noqa: C901
        """
        Get data for the ma_phi field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_phi data.
        """
        if hasattr(instance, 'ma_phi') and instance.ma_phi:
            return {
                'uuid': instance.ma_phi.uuid,
                'ma_phi': instance.ma_phi.ma_phi,
                'ten_phi': instance.ma_phi.ten_phi,
            }
        return None

    def get_ma_sp_data(self, instance):  # noqa: C901
        """
        Get data for the ma_sp field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_sp data.
        """
        if hasattr(instance, 'ma_sp') and instance.ma_sp:
            return {
                'uuid': instance.ma_sp.uuid,
                'ma_vt': instance.ma_sp.ma_vt,
                'ten_vt': instance.ma_sp.ten_vt,
            }
        return None

    def get_ma_cp0_data(self, instance):  # noqa: C901
        """
        Get data for the ma_cp0 field.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance.

        Returns
        -------
        dict
            A dictionary containing the ma_cp0 data.
        """
        if hasattr(instance, 'ma_cp0') and instance.ma_cp0:
            return {
                'uuid': instance.ma_cp0.uuid,
                'ma_cp': instance.ma_cp0.ma_cp,
                'ten_cp': instance.ma_cp0.ten_cp,
            }
        return None

    def create(self, validated_data):  # noqa: C901
        """
        Create a new ChiTietPhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        validated_data: dict
            The validated data for the new instance.

        Returns
        -------
        ChiTietPhieuThanhToanTamUngModel
            The newly created ChiTietPhieuThanhToanTamUngModel instance.
        """
        entity_slug = self.context['entity_slug']
        user_model = self.context['request'].user
        phieu_thanh_toan_uuid = None
        if (
            'phieu_thanh_toan' in validated_data
            and validated_data['phieu_thanh_toan']
        ):
            phieu_thanh_toan_uuid = validated_data.pop('phieu_thanh_toan').uuid
        service = ChiTietPhieuThanhToanTamUngService()
        instance = service.create(
            entity_slug=entity_slug,
            user_model=user_model,
            phieu_thanh_toan_uuid=phieu_thanh_toan_uuid,
            **validated_data,
        )
        return instance

    def update(self, instance, validated_data):  # noqa: C901
        """
        Update an existing ChiTietPhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        instance: ChiTietPhieuThanhToanTamUngModel
            The ChiTietPhieuThanhToanTamUngModel instance to update.
        validated_data: dict
            The validated data for the update.

        Returns
        -------
        ChiTietPhieuThanhToanTamUngModel
            The updated ChiTietPhieuThanhToanTamUngModel instance.
        """
        entity_slug = self.context['entity_slug']
        user_model = self.context['request'].user
        if 'phieu_thanh_toan' in validated_data:
            validated_data.pop('phieu_thanh_toan')

        service = ChiTietPhieuThanhToanTamUngService()
        instance = service.update(
            entity_slug=entity_slug,
            user_model=user_model,
            instance=instance,
            **validated_data,
        )
        return instance
