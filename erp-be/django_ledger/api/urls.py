"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL Configuration for API
"""

from django.urls import include, path  # noqa: F401
from rest_framework.routers import DefaultRouter  # noqa: F401

from django_ledger.api.views.accounts import (  # noqa: F401
    AccountConstraintViewSet,
    AccountModelViewSet,
)
from django_ledger.api.views.ban_hang.don_hang.don_hang.chi_tiet_don_ban_hang import (  # noqa: F401,
    ChiTietDonBanHangViewSet,
)
from django_ledger.api.views.ban_hang.don_hang.don_hang.don_ban_hang import (  # noqa: F401,
    DonBanHangViewSet,
)
from django_ledger.api.views.ban_hang.hoa_don_ban_ra.hoa_don_ban_hang import (  # noqa: F401,
    ChiTietHoaDonBanHangViewSet,
    HoaDonBanHangViewSet,
    ThongTinThanhToanHoaDonBanHangViewSet,
)
from django_ledger.api.views.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (  # noqa: F401,
    ChiTietHoaDonViewSet,
    HoaDonDichVuViewSet,
    ThongTinThanhToanHoaDonDichVuViewSet,
)
from django_ledger.api.views.ban_hang.hoa_don_dien_tu.dieu_chinh_thong_tin_hoa_don_da_xac_thuc import (  # noqa: F401,
    HoaDonDieuChinhThongTinViewSet,
)
from django_ledger.api.views.ban_hang.so_du_tuc_thoi_theo_tai_khoan import (  # noqa: F401,
    SoDuTucThoiTheoTaiKhoanModelViewSet,
)
from django_ledger.api.views.bank_account import BankAccountViewSet  # noqa: F401,
from django_ledger.api.views.bill import BillViewSet  # noqa: F401,
from django_ledger.api.views.CapNhatDieuKienThanhToan import (  # noqa: F401,
    ChiTietDieuKienThanhToanModelViewSet,
    DieuKienThanhToanModelViewSet,
)
from django_ledger.api.views.chart_of_accounts import (  # noqa: F401,
    ChartOfAccountModelViewSet,
)
from django_ledger.api.views.chi_phi import ChiPhiViewSet  # noqa: F401,
from django_ledger.api.views.chung_tu import (  # noqa: F401,
    ChiTietChungTuViewSet,
    ChungTuViewSet,
)

# Tax Authority ViewSets
from django_ledger.api.views.co_quan_thue import CoQuanThueModelViewSet  # noqa: F401,
from django_ledger.api.views.color import ColorModelViewSet  # noqa: F401,
from django_ledger.api.views.contract import ContractViewSet  # noqa: F401,

# Customer ViewSets
from django_ledger.api.views.customer_erp import (  # noqa: F401,
    LoaiGiaBanViewSet,
    NhomKhachHangViewSet,
)

# KheUoc ViewSets are now imported from erp.py
from django_ledger.api.views.danh_muc.ban_hang.hinh_thuc_thanh_toan import (  # noqa: F401,
    HinhThucThanhToanModelViewSet,
)
from django_ledger.api.views.danh_muc.ban_hang.kenh_ban_hang.kenh_ban_hang import (  # noqa: F401,
    KenhBanHangModelViewSet,
)
from django_ledger.api.views.danh_muc.khai_bao_kho_vat_tu.khai_bao_ma_hang_ipos import (  # noqa: F401,
    KhaiBaoMaHangIposViewSet,
)
from django_ledger.api.views.danh_muc.khai_bao_kho_vat_tu.quy_doi_don_vi_tinh_chi_tiet import (  # noqa: F401,
    QuyDoiDonViTinhChiTietViewSet,
)
from django_ledger.api.views.danh_muc.khai_bao_kho_vat_tu.thong_tin_vat_tu_theo_don_vi import (  # noqa: F401,
    ThongTinVatTuTheoDonViViewSet,
)
from django_ledger.api.views.danh_muc_nguon_don import (  # noqa: F401,
    DanhMucNguonDonModelViewSet,
)
from django_ledger.api.views.dia_chi import DiaChiModelViewSet  # noqa: F401,
from django_ledger.api.views.dia_chi_nhan_hang import (  # noqa: F401,
    DiaChiNhanHangModelViewSet,
)
from django_ledger.api.views.dich_vu import DichVuViewSet  # noqa: F401,
from django_ledger.api.views.don_vi_tinh import DonViTinhModelViewSet  # noqa: F401,
from django_ledger.api.views.entity import EntityViewSet  # noqa: F401,

# Import from erp.py for backward compatibility
from django_ledger.api.views.erp import (  # noqa: F401,
    CustomerViewSet,
    SizeViewSet,
    VendorViewSet,
)

# Finance ViewSets
from django_ledger.api.views.finance import (  # noqa: F401,
    NhomPhiViewSet,
    PhuongThucThanhToanViewSet,
    ThueSuatThueGTGTViewSet,
)
from django_ledger.api.views.gia_mua import GiaMuaModelViewSet  # noqa: F401,
from django_ledger.api.views.group import GroupModelViewSet  # noqa: F401,
from django_ledger.api.views.han_thanh_toan import HanThanhToanViewSet  # noqa: F401,
from django_ledger.api.views.invoice import InvoiceViewSet  # noqa: F401,
from django_ledger.api.views.journal_entry import JournalEntryViewSet  # noqa: F401,
from django_ledger.api.views.khu_vuc import KhuVucModelViewSet  # noqa: F401,
from django_ledger.api.views.kich_co import KichCoModelViewSet  # noqa: F401,
from django_ledger.api.views.ledger import LedgerViewSet  # noqa: F401,
from django_ledger.api.views.lo import LoModelViewSet  # noqa: F401,
from django_ledger.api.views.loai_gia_ban import LoaiGiaBanModelViewSet  # noqa: F401,
from django_ledger.api.views.loai_yeu_to import LoaiYeuToViewSet  # noqa: F401,
from django_ledger.api.views.mau_so_hd import MauSoHDModelViewSet  # noqa: F401,

# Misc ViewSets
from django_ledger.api.views.misc import RangBuocNhapViewSet  # noqa: F401,
from django_ledger.api.views.mua_hang import PurchaseModelViewSet  # noqa: F401,
from django_ledger.api.views.mua_hang.don_mua_hang.don_mua_hang import (  # noqa: F401,
    DonMuaHangViewSet,
)
from django_ledger.api.views.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import (  # noqa: F401,
    ChiTietPhieuXuatTraLaiNhaCungCapViewSet,
    PhieuXuatTraLaiNhaCungCapViewSet,
)
from django_ledger.api.views.mua_hang.hoa_don_mua_vao.hoa_don_mua_hang_trong_nuoc import (  # noqa: F401,
    ChiPhiChiTietHoaDonMuaHangTrongNuocViewSet,
    ChiPhiHoaDonMuaHangTrongNuocViewSet,
    ChiTietHoaDonMuaHangTrongNuocViewSet,
    HoaDonMuaHangTrongNuocModelViewSet,
    ThueHoaDonMuaHangTrongNuocViewSet,
)
from django_ledger.api.views.ngan_hang import NganHangViewSet  # noqa: F401,
from django_ledger.api.views.nhan_vien import NhanVienModelViewSet  # noqa: F401,
from django_ledger.api.views.nhap_xuat import NhapXuatModelViewSet  # noqa: F401,
from django_ledger.api.views.nhom_loai_hd import NhomLoaiHDModelViewSet  # noqa: F401,

# Organization ViewSets
from django_ledger.api.views.organization import BoPhanViewSet  # noqa: F401,
from django_ledger.api.views.phi_ngan_hang import PhiNganHangViewSet  # noqa: F401,
from django_ledger.api.views.phuong_tien_giao_hang import (  # noqa: F401,
    PhuongTienGiaoHangModelViewSet,
)
from django_ledger.api.views.phuong_tien_van_chuyen import (  # noqa: F401,
    PhuongTienVanChuyenModelViewSet,
)
from django_ledger.api.views.profile import profile_view  # noqa: F401,
from django_ledger.api.views.quan_huyen import QuanHuyenModelViewSet  # noqa: F401,
from django_ledger.api.views.quoc_gia import QuocGiaModelViewSet  # noqa: F401,
from django_ledger.api.views.quy_cach import QuyCachViewSet  # noqa: F401,
from django_ledger.api.views.quy_doi_don_vi_tinh_chung import (  # noqa: F401,
    QuyDoiDonViTinhChungViewSet,
)
from django_ledger.api.views.quyen_chung_tu import QuyenChungTuViewSet  # noqa: F401,
from django_ledger.api.views.report import ReportViewSet  # noqa: F401,
from django_ledger.api.views.sql_query import SQLQueryView  # noqa: F401,
from django_ledger.api.views.tai_khoan_cua_hang import (  # noqa: F401,
    TaiKhoanCuaHangModelViewSet,
)
from django_ledger.api.views.tax import TaxModelViewSet  # noqa: F401,
from django_ledger.api.views.tien_do_thanh_toan import (  # noqa: F401,
    TienDoThanhToanViewSet,
)
from django_ledger.api.views.tinh_chat_thue import (  # noqa: F401,
    TinhChatThueModelViewSet,
)
from django_ledger.api.views.tinh_thanh import TinhThanhModelViewSet  # noqa: F401,
from django_ledger.api.views.ton_kho.nhap_kho_noi_bo.phieu_nhap_kho import (  # noqa: F401,
    ChiTietPhieuNhapKhoViewSet,
    PhieuNhapKhoViewSet,
)
from django_ledger.api.views.ty_gia import TyGiaModelViewSet  # noqa: F401,
from django_ledger.api.views.unit import EntityUnitModelViewSet  # noqa: F401,
from django_ledger.api.views.user import UserViewSet  # noqa: F401,
from django_ledger.api.views.vat_tu import VatTuViewSet  # noqa: F401,
from django_ledger.api.views.vat_tu_quy_doi_dvt import (  # noqa: F401,
    VatTuQuyDoiDVTViewSet,
)
from django_ledger.api.views.vi_tri import ViTriViewSet  # noqa: F401,
from django_ledger.api.views.vi_tri_kho_hang import (  # noqa: F401,
    ViTriKhoHangModelViewSet,
)
from django_ledger.api.views.vu_viec import VuViecModelViewSet  # noqa: F401,

# Warehouse ViewSets
from django_ledger.api.views.warehouse import (  # noqa: F401,
    KhoHangViewSet,
    LoaiVatTuViewSet,
    NhomHangViewSet,
    VatTuSanPhamDonViTinhViewSet,
    VatTuSanPhamHangHoaViewSet,
)
from django_ledger.api.views.xa_phuong import XaPhuongModelViewSet  # noqa: F401,

# No need to import routers directly as we're using include

# Main router
router = DefaultRouter()
# Register entity viewset at root level
router.register("entities", EntityViewSet, basename="entity")
# Register user viewset at root level
router.register("users", UserViewSet, basename="user")
# Register nested viewsets
ledger_router = DefaultRouter()
ledger_router.register(
    "journal-entries", JournalEntryViewSet, basename="journal-entries"
)

# Register main viewsets
router.register("ledgers", LedgerViewSet, basename="ledgers")
router.register("invoices", InvoiceViewSet, basename="invoices")
router.register("bills", BillViewSet, basename="bills")
# Create ERP router - These will be registered under entity namespace
erp_router = DefaultRouter()
erp_router.register("accounts", AccountModelViewSet, basename="accounts")
erp_router.register("materials", VatTuViewSet, basename="materials")
erp_router.register("warehouses", KhoHangViewSet, basename="warehouses")
erp_router.register("employees", NhanVienModelViewSet, basename="employees")
erp_router.register("exchange-rates", TyGiaModelViewSet, basename="exchange-rates")
erp_router.register("payment-terms", HanThanhToanViewSet, basename="payment-terms")
erp_router.register(
    "payment-methods", PhuongThucThanhToanViewSet, basename="payment-methods"
)
erp_router.register(
    "hinh-thuc-thanh-toan",
    HinhThucThanhToanModelViewSet,
    basename="hinh-thuc-thanh-toan",
)
erp_router.register("product-types", LoaiVatTuViewSet, basename="product-types")
erp_router.register("product-groups", NhomHangViewSet, basename="product-groups")
erp_router.register("tax-rates", ThueSuatThueGTGTViewSet, basename="tax-rates")
erp_router.register(
    "product-units", VatTuSanPhamDonViTinhViewSet, basename="product-units"
)
erp_router.register(
    "product-variants", VatTuSanPhamHangHoaViewSet, basename="product-variants"
)
erp_router.register("purchase-prices", GiaMuaModelViewSet, basename="purchase-prices")
# GiaBanViewSet moved to danh_muc/ban_hang/gia_ban
erp_router.register(
    "material-unit-conversions",
    VatTuQuyDoiDVTViewSet,
    basename="material-unit-conversions",
)
erp_router.register(
    "input-constraints", RangBuocNhapViewSet, basename="input-constraints"
)
# Loan endpoints moved to /erp/danh_muc/hop_dong_khe_uoc/khe_uoc
erp_router.register("fee-groups", NhomPhiViewSet, basename="fee-groups")
erp_router.register(
    "sales-price-types", LoaiGiaBanModelViewSet, basename="sales-price-types"
)
erp_router.register("customer-groups", NhomKhachHangViewSet, basename="customer-groups")
erp_router.register("countries", QuocGiaModelViewSet, basename="countries")
erp_router.register("contracts", ContractViewSet, basename="contracts")
erp_router.register(
    "payment-schedules", TienDoThanhToanViewSet, basename="payment-schedules"
)
erp_router.register("provinces", TinhThanhModelViewSet, basename="provinces")
erp_router.register("units", DonViTinhModelViewSet, basename="units")
erp_router.register("purchases", PurchaseModelViewSet, basename="purchases")
erp_router.register("customers", CustomerViewSet, basename="customers")
erp_router.register("vendors", VendorViewSet, basename="vendors")
erp_router.register("districts", QuanHuyenModelViewSet, basename="districts")
erp_router.register("wards", XaPhuongModelViewSet, basename="wards")
erp_router.register("regions", KhuVucModelViewSet, basename="regions")
erp_router.register("departments", BoPhanViewSet, basename="departments")
erp_router.register("sizes", KichCoModelViewSet, basename="sizes")
erp_router.register("cases", VuViecModelViewSet, basename="cases")
erp_router.register("lots", LoModelViewSet, basename="lots")
erp_router.register("taxes", TaxModelViewSet, basename="taxes")
erp_router.register("groups", GroupModelViewSet, basename="groups")
erp_router.register(
    "tax-authorities", CoQuanThueModelViewSet, basename="tax-authorities"
)
erp_router.register("tax-natures", TinhChatThueModelViewSet, basename="tax-natures")
erp_router.register("colors", ColorModelViewSet, basename="colors")
erp_router.register("positions", ViTriViewSet, basename="positions")
erp_router.register(
    "warehouse-locations",
    ViTriKhoHangModelViewSet,
    basename="warehouse-locations",
)
# Removed direct registration of ChiTietChiTieuNganSachViewSet
# Now registered through the dedicated router in api/routers/ngan_sach/chi_tieu_ngan_sach/  # noqa: E501
erp_router.register("factor-types", LoaiYeuToViewSet, basename="factor-types")
erp_router.register("expenses", ChiPhiViewSet, basename="expenses")
erp_router.register("specifications", QuyCachViewSet, basename="specifications")
erp_router.register("services", DichVuViewSet, basename="services")
erp_router.register("banks", NganHangViewSet, basename="banks")
erp_router.register("bank-accounts", BankAccountViewSet, basename="bank-accounts")
erp_router.register("bank-fees", PhiNganHangViewSet, basename="bank-fees")
erp_router.register("addresses", KhuVucModelViewSet, basename="addresses")
erp_router.register("don-mua-hang", DonMuaHangViewSet, basename="don-mua-hang")
erp_router.register(
    "unit-conversions", QuyDoiDonViTinhChungViewSet, basename="unit-conversions"
)
erp_router.register("documents", ChungTuViewSet, basename="documents")
erp_router.register("dia-chi", DiaChiModelViewSet, basename="dia-chi")
erp_router.register(
    "delivery-addresses",
    DiaChiNhanHangModelViewSet,
    basename="delivery-addresses",
)
erp_router.register(
    "shipping-methods",
    PhuongTienVanChuyenModelViewSet,
    basename="shipping-methods",
)
erp_router.register(
    "delivery-methods",
    PhuongTienGiaoHangModelViewSet,
    basename="delivery-methods",
)
erp_router.register(
    "import-exports", NhapXuatModelViewSet, basename="import-exports"
)  # noqa: E402,
erp_router.register(
    "order-sources", DanhMucNguonDonModelViewSet, basename="order-sources"
)
erp_router.register(
    "store-accounts", TaiKhoanCuaHangModelViewSet, basename="store-accounts"
)
erp_router.register("entity-units", EntityUnitModelViewSet, basename="entity-units")
erp_router.register("document-books", QuyenChungTuViewSet, basename="document-books")
erp_router.register(
    "contract-type-groups",
    NhomLoaiHDModelViewSet,
    basename="contract-type-groups",
)
erp_router.register(
    "contract-number-formats",
    MauSoHDModelViewSet,
    basename="contract-number-formats",
)
erp_router.register(
    "payment-conditions",
    DieuKienThanhToanModelViewSet,
    basename="payment-conditions",
)
erp_router.register(
    "payment-condition-details",
    ChiTietDieuKienThanhToanModelViewSet,
    basename="payment-condition-details",
)
erp_router.register(
    "detailed-unit-conversions",
    QuyDoiDonViTinhChiTietViewSet,
    basename="detailed-unit-conversions",
)
erp_router.register(
    "material-unit-info",
    ThongTinVatTuTheoDonViViewSet,
    basename="material-unit-info",
)
erp_router.register(
    "ipos-material-codes",
    KhaiBaoMaHangIposViewSet,
    basename="ipos-material-codes",
)
erp_router.register("sales-channels", KenhBanHangModelViewSet, basename="kenh-ban-hang")
erp_router.register(
    "real-time-account-balances",
    SoDuTucThoiTheoTaiKhoanModelViewSet,
    basename="real-time-account-balances",
)
# URL patterns with nested routes
urlpatterns = [
    # Root-level router URLs (includes entities endpoint)
    path("", include(router.urls)),
    path("profile/", profile_view, name="user-profile"),
    path("sql-query/", SQLQueryView.as_view(), name="sql-query"),
    # Entity-specific endpoints
    path(
        "entities/<slug:entity_slug>/",
        include(
            [
                # Chart of Accounts endpoints
                path(
                    "coa/",
                    include(
                        [
                            # List and Create CoA
                            path(
                                "",
                                ChartOfAccountModelViewSet.as_view(
                                    {"get": "list", "post": "create"}
                                ),
                                name="coa-list",
                            ),
                            # CoA Detail level operations
                            path(
                                "<slug:slug>/",
                                include(
                                    [
                                        # Retrieve, Update, Delete CoA
                                        path(
                                            "",
                                            ChartOfAccountModelViewSet.as_view(
                                                {
                                                    "get": "retrieve",
                                                    "patch": "partial_update",
                                                    "delete": "destroy",
                                                }
                                            ),
                                            name="coa-detail",
                                        ),
                                        # CoA Actions
                                        path(
                                            "mark-default/",
                                            ChartOfAccountModelViewSet.as_view(
                                                {"post": "mark_default"}
                                            ),
                                            name="coa-mark-default",
                                        ),
                                        path(
                                            "mark-active/",
                                            ChartOfAccountModelViewSet.as_view(
                                                {"post": "mark_active"}
                                            ),
                                            name="coa-mark-active",
                                        ),
                                        path(
                                            "mark-inactive/",
                                            ChartOfAccountModelViewSet.as_view(
                                                {"post": "mark_inactive"}
                                            ),
                                            name="coa-mark-inactive",
                                        ),
                                        # Accounts under this CoA
                                        path(
                                            "accounts/",
                                            include(
                                                [
                                                    # Account list and create
                                                    path(
                                                        "",
                                                        AccountModelViewSet.as_view(
                                                            {
                                                                "get": "list",
                                                                "post": "create",
                                                            }
                                                        ),
                                                        name="account-list",
                                                    ),
                                                    # Account filtering by prefix
                                                    path(
                                                        "by-prefix/",
                                                        AccountModelViewSet.as_view(
                                                            {"get": "by_prefix"}
                                                        ),
                                                        name="account-by-prefix",
                                                    ),
                                                    # Account filtering by type
                                                    path(
                                                        "fixed-assets/",
                                                        AccountModelViewSet.as_view(
                                                            {"get": "fixed_assets"}
                                                        ),
                                                        name="account-fixed-assets",
                                                    ),
                                                    path(
                                                        "current-assets/",
                                                        AccountModelViewSet.as_view(
                                                            {"get": "current_assets"}
                                                        ),
                                                        name="account-current-assets",
                                                    ),
                                                    path(
                                                        "liabilities/",
                                                        AccountModelViewSet.as_view(
                                                            {"get": "liabilities"}
                                                        ),
                                                        name="account-liabilities",
                                                    ),
                                                    path(
                                                        "equity/",
                                                        AccountModelViewSet.as_view(
                                                            {"get": "equity"}
                                                        ),
                                                        name="account-equity",
                                                    ),
                                                    path(
                                                        "revenue/",
                                                        AccountModelViewSet.as_view(
                                                            {"get": "revenue"}
                                                        ),
                                                        name="account-revenue",
                                                    ),
                                                    path(
                                                        "expenses/",
                                                        AccountModelViewSet.as_view(
                                                            {"get": "expenses"}
                                                        ),
                                                        name="account-expenses",
                                                    ),
                                                    # Account detail operations
                                                    path(
                                                        "<uuid:uuid>/",
                                                        include(
                                                            [
                                                                path(
                                                                    "",
                                                                    AccountModelViewSet.as_view(  # noqa: E501
                                                                        {
                                                                            "get": "retrieve",
                                                                            "put": "update",
                                                                            "patch": "partial_update",
                                                                            "delete": "destroy",
                                                                        }
                                                                    ),
                                                                    name="account-detail",  # noqa: E501
                                                                ),
                                                                # Account constraints
                                                                path(
                                                                    "constraints/",
                                                                    include(
                                                                        [
                                                                            path(
                                                                                "",
                                                                                AccountConstraintViewSet.as_view(  # noqa: E501
                                                                                    {
                                                                                        "get": "list",
                                                                                        "post": "create",
                                                                                    }
                                                                                ),
                                                                                name="account-constraint-list",  # noqa: E501
                                                                            ),
                                                                            path(
                                                                                "<int:pk>/",  # noqa: E501
                                                                                AccountConstraintViewSet.as_view(  # noqa: E501
                                                                                    {
                                                                                        "get": "retrieve",
                                                                                        "put": "update",
                                                                                        "patch": "partial_update",
                                                                                        "delete": "destroy",
                                                                                    }
                                                                                ),
                                                                                name="account-constraint-detail",  # noqa: E501
                                                                            ),
                                                                        ]
                                                                    ),
                                                                ),
                                                            ]
                                                        ),
                                                    ),
                                                ]
                                            ),
                                        ),
                                    ]
                                ),
                            ),
                        ]
                    ),
                ),
                # ERP endpoints - all entity-related models
                path(
                    "erp/",
                    include(
                        [
                            # Custom endpoint for document-books with query parameters (must be before router)
                            path(
                                "document-books/by-chung-tu/",
                                QuyenChungTuViewSet.as_view({"get": "get_quyen_chung_tu_by_ct"}),
                                name="document-books-by-chung-tu",
                            ),
                            # Include all standard ERP routes
                            path("", include(erp_router.urls)),
                            # Include all routers from the routers package
                            path("", include("django_ledger.api.routers.urls")),
                            # Nested routes for documents (ChungTu) and their details (ChiTietChungTu)
                            path(
                                "documents/<uuid:chung_tu_uuid>/chi-tiet/",
                                include(
                                    [
                                        # List and Create ChiTietChungTu
                                        path(
                                            "",
                                            ChiTietChungTuViewSet.as_view(
                                                {"get": "list", "post": "create"}
                                            ),
                                            name="document-details-list",
                                        ),
                                        # ChiTietChungTu Detail operations
                                        path(
                                            "<uuid:uuid>/",
                                            ChiTietChungTuViewSet.as_view(
                                                {
                                                    "get": "retrieve",
                                                    "put": "update",
                                                    "delete": "destroy",
                                                }
                                            ),
                                            name="document-details-detail",
                                        ),
                                    ]
                                ),
                            ),

                            # Nested routes for phieu-nhap-kho
                        ]
                    ),
                ),
                # Entity-level reports
                path(
                    "reports/",
                    include(
                        [
                            path(
                                "balance-sheet/",
                                ReportViewSet.as_view({"get": "balance_sheet"}),
                                name="entity-balance-sheet",
                            ),
                            path(
                                "income-statement/",
                                ReportViewSet.as_view({"get": "income_statement"}),
                                name="entity-income-statement",
                            ),
                            path(
                                "cash-flow-statement/",
                                ReportViewSet.as_view({"get": "cash_flow_statement"}),
                                name="entity-cash-flow",
                            ),
                            path(
                                "trial-balance/",
                                ReportViewSet.as_view({"get": "trial_balance"}),
                                name="entity-trial-balance",
                            ),
                        ]
                    ),
                ),
                # Bill endpoints
                path(
                    "bills/",
                    include(
                        [
                            path("", include(router.urls)),
                            # Bill state changes
                            path(
                                "<uuid:bill_uuid>/mark-as-review/",
                                BillViewSet.as_view({"post": "mark_as_review"}),
                                name="bill-mark-review",
                            ),
                            path(
                                "<uuid:bill_uuid>/mark-as-approved/",
                                BillViewSet.as_view({"post": "mark_as_approved"}),
                                name="bill-mark-approved",
                            ),
                            path(
                                "<uuid:bill_uuid>/mark-as-paid/",
                                BillViewSet.as_view({"post": "mark_as_paid"}),
                                name="bill-mark-paid",
                            ),
                            # Vendor list
                            path(
                                "vendors/",
                                BillViewSet.as_view({"get": "get_vendors"}),
                                name="bill-vendors",
                            ),
                        ]
                    ),
                ),
                # Invoice endpoints
                path(
                    "invoices/",
                    include(
                        [
                            path("", include(router.urls)),
                            # Invoice state changes
                            path(
                                "<uuid:invoice_uuid>/mark-as-review/",
                                InvoiceViewSet.as_view({"post": "mark_as_review"}),
                                name="invoice-mark-review",
                            ),
                            path(
                                "<uuid:invoice_uuid>/mark-as-approved/",
                                InvoiceViewSet.as_view({"post": "mark_as_approved"}),
                                name="invoice-mark-approved",
                            ),
                            path(
                                "<uuid:invoice_uuid>/mark-as-paid/",
                                InvoiceViewSet.as_view({"post": "mark_as_paid"}),
                                name="invoice-mark-paid",
                            ),
                            # Customer list
                            path(
                                "customers/",
                                InvoiceViewSet.as_view({"get": "get_customers"}),
                                name="invoice-customers",
                            ),
                        ]
                    ),
                ),
                # Ledger endpoints
                path(
                    "ledgers/",
                    include(
                        [
                            # Ledger basic operations
                            path("", include(router.urls)),
                            # Ledger-specific reports
                            path(
                                "<uuid:ledger_uuid>/balance-sheet/",
                                LedgerViewSet.as_view({"get": "balance_sheet"}),
                                name="ledger-balance-sheet",
                            ),
                            path(
                                "<uuid:ledger_uuid>/income-statement/",
                                LedgerViewSet.as_view({"get": "income_statement"}),
                                name="ledger-income-statement",
                            ),
                            path(
                                "<uuid:ledger_uuid>/cash-flow-statement/",
                                LedgerViewSet.as_view({"get": "cash_flow_statement"}),
                                name="ledger-cash-flow",
                            ),
                            path(
                                "<uuid:ledger_uuid>/trial-balance/",
                                LedgerViewSet.as_view({"get": "trial_balance"}),
                                name="ledger-trial-balance",
                            ),
                            # Ledger state changes
                            path(
                                "<uuid:ledger_uuid>/post/",
                                LedgerViewSet.as_view({"post": "post_ledger"}),
                                name="ledger-post",
                            ),
                            path(
                                "<uuid:ledger_uuid>/unpost/",
                                LedgerViewSet.as_view({"post": "unpost_ledger"}),
                                name="ledger-unpost",
                            ),
                            # Journal entries under ledger
                            path(
                                "<uuid:ledger_uuid>/journal-entries/",
                                include(ledger_router.urls),
                            ),
                        ]
                    ),
                ),
            ]
        ),
    ),
    # Ledger URLs
]

# API Documentation - Optional, only added if dependencies are available
API_TITLE = "Django Ledger API"
API_DESCRIPTION = "A Web API for managing accounting operations."
# Try to add documentation if dependencies are available
try:
    from rest_framework.documentation import include_docs_urls  # noqa: F401,
    from rest_framework.schemas import get_schema_view  # noqa: F401,

    # If we got here, the dependencies are available
    schema_view = get_schema_view(title=API_TITLE)
    urlpatterns += [
        path("schema/", schema_view),
        path(
            "docs/",
            include_docs_urls(title=API_TITLE, description=API_DESCRIPTION),
        ),
    ]
except (ImportError, AssertionError):
    # Either rest_framework.schemas or coreapi is not available
    # Just skip adding documentation endpoints
    pass
