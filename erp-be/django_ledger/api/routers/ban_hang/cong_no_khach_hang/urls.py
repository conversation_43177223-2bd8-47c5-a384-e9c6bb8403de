"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URL patterns for Cong No Khach Hang (Customer Debt) module.
"""

from django.urls import path, include

# URL patterns for the module
urlpatterns = [
    # Include bang_can_doi_phat_sinh_cong_no URLs
    path('bang-can-doi-phat-sinh-cong-no/', include('django_ledger.api.routers.ban_hang.cong_no_khach_hang.bang_can_doi_phat_sinh_cong_no.urls')),

    # Include so_chi_tiet_cong_no_theo_khach_hang URLs
    path('so-chi-tiet-cong-no-theo-khach-hang/', include('django_ledger.api.routers.ban_hang.cong_no_khach_hang.so_chi_tiet_cong_no_theo_khach_hang.urls')),

    # Include so_chi_tiet_cong_no_nhieu_khach_hang URLs
    path('so-chi-tiet-cong-no-nhieu-khach-hang/', include('django_ledger.api.routers.ban_hang.cong_no_khach_hang.so_chi_tiet_cong_no_nhieu_khach_hang.urls')),

    # Include bang_xac_nhan_cong_no URLs
    path('bang-xac-nhan-cong-no/', include('django_ledger.api.routers.ban_hang.cong_no_khach_hang.bang_xac_nhan_cong_no.urls')),

    # Add other cong_no_khach_hang-related URLs here
]
