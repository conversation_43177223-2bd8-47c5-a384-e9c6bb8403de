"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for So Chi Tiet Cong No Nhieu Khach Hang (Multiple Customer Debt Detail Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.ban_hang.cong_no_khach_hang.so_chi_tiet_cong_no_nhieu_khach_hang import SoChiTietCongNoNhieuKhachHangViewSet

# URL patterns - Single endpoint for multiple customer debt detail report with filters as POST body data
urlpatterns = [
    # Multiple Customer Debt Detail Report endpoint - returns report directly with filter POST body data
    path("", SoChiTietCongNoNhieuKhachHangViewSet.as_view({"post": "get_report"}), name="so-chi-tiet-cong-no-nhieu-khach-hang-report"),

    # Additional endpoints for testing and health check

]
