"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Bang Xac Nhan Cong No (Debt Confirmation Report) API endpoints.
"""

from django.urls import path
from django_ledger.api.views.ban_hang.cong_no_khach_hang.bang_xac_nhan_cong_no import BangXacNhanCongNoViewSet

# URL patterns - Single endpoint for debt confirmation report with filters as POST body data
urlpatterns = [
    # Debt Confirmation Report endpoint - returns report directly with filter POST body data
    path("", BangXacNhanCongNoViewSet.as_view({"post": "get_report"}), name="bang-xac-nhan-cong-no-report"),
]
