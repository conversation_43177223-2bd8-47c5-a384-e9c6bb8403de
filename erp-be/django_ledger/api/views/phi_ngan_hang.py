"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for PhiNganHang model.
"""

from uuid import UUID  # noqa: F401

from django.utils.translation import gettext_lazy as _  # noqa: F401
from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status  # noqa: F401,
from rest_framework.request import Request  # noqa: F401,
from rest_framework.response import Response  # noqa: F401,

from django_ledger.api.serializers.phi_ngan_hang import (  # noqa: F401,
    PhiNganHangModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models import (  # noqa: F401,
    EntityModel,
    PhiNganHangModel,
)
from django_ledger.services.phi_ngan_hang.service import (  # noqa: F401,
    PhiNganHangService,
)


@extend_schema_view(
    list=extend_schema(tags=["Bank Fees"]),
    create=extend_schema(tags=["Bank Fees"]),
    retrieve=extend_schema(tags=["Bank Fees"]),
    update=extend_schema(tags=["Bank Fees"]),
    partial_update=extend_schema(tags=["Bank Fees"]),
    destroy=extend_schema(tags=["Bank Fees"]),
)
class PhiNganHangViewSet(EntityRelatedViewSet):
    """
    A ViewSet for PhiNganHangModel (Bank Fee)
    """

    serializer_class = PhiNganHangModelSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    queryset = PhiNganHangModel.objects.all()

    def get_service(self) -> PhiNganHangService:  # noqa: C901
        """
        Returns the service for PhiNganHangModel.

        Returns
        -------
        PhiNganHangService
            The service for PhiNganHangModel.
        """
        return PhiNganHangService()

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List all bank fees
        """
        try:
            service = self.get_service()
            queryset = service.get_queryset()
            # Apply filters
            status_filter = request.query_params.get('status')
            if status_filter:
                queryset = queryset.filter(status=status_filter)
            # Apply pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
            
    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new bank fee
        """
        try:
            entity_slug = kwargs.get('entity_slug')
            entity_model = EntityModel.objects.get(slug__exact=entity_slug)
            service = self.get_service()
            data = request.data.copy()
            data['entity_model'] = entity_model
            phi_ngan_hang = service.create_phi_ngan_hang(data)
            serializer = self.get_serializer(phi_ngan_hang)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except EntityModel.DoesNotExist:
            return Response(
                {'error': _('Entity not found.')},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValueError as e:
            return Response(
                {'error': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Get a specific bank fee by UUID
        """
        try:
            service = self.get_service()
            uuid = kwargs.get('pk')
            phi_ngan_hang = service.get_by_uuid(uuid)
            serializer = self.get_serializer(phi_ngan_hang)
            return Response(serializer.data)
        except PhiNganHangModel.DoesNotExist:
            return Response(
                {'error': _('Bank fee not found.')},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a bank fee
        """
        try:
            service = self.get_service()
            uuid = kwargs.get('pk')
            data = request.data.copy()
            phi_ngan_hang = service.update_phi_ngan_hang(uuid, data)
            serializer = self.get_serializer(phi_ngan_hang)
            return Response(serializer.data)
        except PhiNganHangModel.DoesNotExist:
            return Response(
                {'error': _('Bank fee not found.')},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a bank fee
        """
        try:
            service = self.get_service()
            uuid = kwargs.get('pk')
            service.delete_phi_ngan_hang(uuid)
            return Response(status=status.HTTP_204_NO_CONTENT)
        except PhiNganHangModel.DoesNotExist:
            return Response(
                {'error': _('Bank fee not found.')},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
