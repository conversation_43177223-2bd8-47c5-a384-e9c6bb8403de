"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for LoaiTaiSanCongCu model.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.serializers.danh_muc import (  # noqa: F401,
    LoaiTaiSanCongCuModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models.danh_muc import (  # noqa: F401,
    LoaiTaiSanCongCuModel,
)
from django_ledger.services.danh_muc import (  # noqa: F401,
    LoaiTaiSanCongCuService,
)
from django_ledger.api.decorators import api_exception_handler

@extend_schema_view(
    list=extend_schema(
        summary="List LoaiTaiSanCongCu",
        description="Retrieve a list of asset types (LoaiTaiSanCongCu) for the authenticated user's entity.",  # noqa: E501
        tags=["Loai Tai San Cong Cu"],
    ),
    create=extend_schema(
        summary="Create LoaiTaiSanCongCu",
        description="Create a new asset type (LoaiTaiSanCongCu) for the authenticated user's entity.",  # noqa: E501
        tags=["Loai Tai San Cong Cu"],
    ),
    retrieve=extend_schema(
        summary="Retrieve LoaiTaiSanCongCu",
        description="Retrieve a specific asset type (LoaiTaiSanCongCu) by UUID.",
        tags=["Loai Tai San Cong Cu"],
    ),
    update=extend_schema(
        summary="Update LoaiTaiSanCongCu",
        description="Update a specific asset type (LoaiTaiSanCongCu) by UUID.",
        tags=["Loai Tai San Cong Cu"],
    ),
    partial_update=extend_schema(
        summary="Partial Update LoaiTaiSanCongCu",
        description="Partially update a specific asset type (LoaiTaiSanCongCu) by UUID.",  # noqa: E501
        tags=["Loai Tai San Cong Cu"],
    ),
    destroy=extend_schema(
        summary="Delete LoaiTaiSanCongCu",
        description="Delete a specific asset type (LoaiTaiSanCongCu) by UUID.",
        tags=["Loai Tai San Cong Cu"],
    ),
)
class LoaiTaiSanCongCuViewSet(EntityRelatedViewSet):
    """
    ViewSet for managing LoaiTaiSanCongCu (Asset Type) records.

    This viewset provides CRUD operations for asset types within the context
    of a specific entity. All operations are scoped to the authenticated user's entity.
    """

    queryset = LoaiTaiSanCongCuModel.objects.all()
    serializer_class = LoaiTaiSanCongCuModelSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the viewset with the service.
        """
        super().__init__(*args, **kwargs)
        self.service = LoaiTaiSanCongCuService()

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List LoaiTaiSanCongCu instances.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug from kwargs
        entity_slug = self.kwargs['entity_slug']
        # Get query parameters
        search_query = request.query_params.get('search', None)
        status_filter = request.query_params.get('status', None)
        # Get data from service, passing entity_slug directly
        instances = self.service.list(
            entity_slug=entity_slug,
            search_query=search_query,
            status=status_filter,
        )

        # Always paginate the queryset
        page = self.paginate_queryset(instances)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new LoaiTaiSanCongCu instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Validate data with context
        serializer = self.get_serializer(
            data=request.data,
            context={'entity_slug': entity_slug, 'request': request},
        )
        serializer.is_valid(raise_exception=True)
        # Create instance using service
        instance = self.service.create(
            entity_slug=entity_slug, data=serializer.validated_data
        )

        # Serialize response
        response_serializer = self.get_serializer(instance)
        # Return response
        return Response(
            response_serializer.data, status=status.HTTP_201_CREATED
        )

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a LoaiTaiSanCongCu instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Get instance
        instance = self.service.get(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Serialize response
        serializer = self.get_serializer(instance)
        # Return response
        return Response(serializer.data)

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a LoaiTaiSanCongCu instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Validate data
        serializer = self.get_serializer(
            data=request.data,
            context={'entity_slug': entity_slug, 'request': request},
        )
        serializer.is_valid(raise_exception=True)
        try:
            # Update instance
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=kwargs['uuid'],
                data=serializer.validated_data,
            )
        except LoaiTaiSanCongCuModel.DoesNotExist:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Serialize response
        response_serializer = self.get_serializer(instance)
        # Return response
        return Response(response_serializer.data)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a LoaiTaiSanCongCu instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        try:
            # Delete instance
            self.service.delete(entity_slug=entity_slug, uuid=kwargs['uuid'])
        except LoaiTaiSanCongCuModel.DoesNotExist:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Return response
        return Response(status=status.HTTP_204_NO_CONTENT)
