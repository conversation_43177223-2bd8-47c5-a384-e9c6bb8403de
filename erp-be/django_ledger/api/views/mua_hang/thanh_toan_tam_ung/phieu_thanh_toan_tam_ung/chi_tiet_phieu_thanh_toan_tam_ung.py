"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuThanhToanTamUng (Advance Payment Settlement Voucher Detail) View
"""

from rest_framework import status, viewsets
from rest_framework.response import Response

from django_ledger.api.serializers.mua_hang.thanh_toan_tam_ung import (
    ChiTietPhieuThanhToanTamUngSerializer,
)
from django_ledger.services.mua_hang.thanh_toan_tam_ung import (  # noqa: E402
    ChiTietPhieuThanhToanTamUngService,
)
from django_ledger.api.decorators import api_exception_handler

class ChiTietPhieuThanhToanTamUngViewSet(viewsets.ModelViewSet):
    """
    A viewset for the ChiTietPhieuThanhToanTamUngModel.
    """

    serializer_class = ChiTietPhieuThanhToanTamUngSerializer  # noqa: F811
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = ChiTietPhieuThanhToanTamUngService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            A QuerySet of ChiTietPhieuThanhToanTamUngModel instances.
        """
        entity_slug = self.kwargs['entity_slug']
        user_model = self.request.user
        phieu_thanh_toan_uuid = self.kwargs.get('phieu_thanh_toan_uuid')
        return self.service.list(
            entity_slug=entity_slug,
            user_model=user_model,
            phieu_thanh_toan_uuid=phieu_thanh_toan_uuid,
        )

    def get_serializer_context(self):  # noqa: C901
        """
        Get the serializer context.

        Returns
        -------
        dict
            The serializer context.
        """
        context = super().get_serializer_context()
        context['entity_slug'] = self.kwargs['entity_slug']
        return context

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new ChiTietPhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        phieu_thanh_toan_uuid = self.kwargs.get('phieu_thanh_toan_uuid')
        if phieu_thanh_toan_uuid:
            request.data['phieu_thanh_toan'] = phieu_thanh_toan_uuid
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing ChiTietPhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        phieu_thanh_toan_uuid = self.kwargs.get('phieu_thanh_toan_uuid')
        if phieu_thanh_toan_uuid:
            request.data['phieu_thanh_toan'] = phieu_thanh_toan_uuid
        serializer = self.get_serializer(
            instance, data=request.data, partial=partial
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data)
        
    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a ChiTietPhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        instance = self.get_object()
        self.service.delete(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
            instance=instance,
        )

        return Response(status=status.HTTP_204_NO_CONTENT)
