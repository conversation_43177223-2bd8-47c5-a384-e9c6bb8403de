"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuThanhToanTamUng (Advance Payment Settlement Voucher) View
"""

from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

from django_ledger.api.serializers.mua_hang.thanh_toan_tam_ung import (  # noqa: E402
    ChiTietPhieuThanhToanTamUngSerializer,
    PhieuThanhToanTamUngSerializer,
    QuyetToanCacLanTamUngSerializer,
    ThuePhieuThanhToanTamUngSerializer,
)
from django_ledger.services.mua_hang.thanh_toan_tam_ung import (  # noqa: E402
    ChiTietPhieuThanhToanTamUngService,
    PhieuThanhToanTamUngService,
    QuyetToanCacLanTamUngService,
    ThuePhieuThanhToanTamUngService,
)
from django_ledger.api.decorators import api_exception_handler

class PhieuThanhToanTamUngViewSet(viewsets.ModelViewSet):
    """
    A viewset for the PhieuThanhToanTamUngModel.
    """

    serializer_class = PhieuThanhToanTamUngSerializer  # noqa: F811
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = PhieuThanhToanTamUngService()
        self.chi_tiet_service = ChiTietPhieuThanhToanTamUngService()
        self.thue_service = ThuePhieuThanhToanTamUngService()
        self.quyet_toan_service = QuyetToanCacLanTamUngService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the viewset.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuThanhToanTamUngModel instances.
        """
        entity_slug = self.kwargs['entity_slug']
        user_model = self.request.user
        return self.service.list(entity_slug=entity_slug, user_model=user_model)

    def get_serializer_context(self):  # noqa: C901
        """
        Get the serializer context.

        Returns
        -------
        dict
            The serializer context.
        """
        context = super().get_serializer_context()
        context['entity_slug'] = self.kwargs['entity_slug']
        return context

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        # Process chi_tiet_phieu_thanh_toan if provided
        if 'chi_tiet_phieu_thanh_toan' in request.data and isinstance(
            request.data['chi_tiet_phieu_thanh_toan'], list
        ):
            for chi_tiet_data in request.data['chi_tiet_phieu_thanh_toan']:
                chi_tiet_data['phieu_thanh_toan'] = instance.uuid
                chi_tiet_serializer = ChiTietPhieuThanhToanTamUngSerializer(
                    data=chi_tiet_data,
                    context=self.get_serializer_context(),
                )
                chi_tiet_serializer.is_valid(raise_exception=True)
                chi_tiet_serializer.save()

        # Process thue_phieu_thanh_toan if provided
        if 'thue_phieu_thanh_toan' in request.data and isinstance(
            request.data['thue_phieu_thanh_toan'], list
        ):
            for thue_data in request.data['thue_phieu_thanh_toan']:
                thue_data['phieu_thanh_toan'] = instance.uuid
                thue_serializer = ThuePhieuThanhToanTamUngSerializer(
                    data=thue_data, context=self.get_serializer_context()
                )
                thue_serializer.is_valid(raise_exception=True)
                thue_serializer.save()

        # Process quyet_toan_tam_ung if provided
        if 'quyet_toan_tam_ung' in request.data and isinstance(
            request.data['quyet_toan_tam_ung'], list
        ):
            for quyet_toan_data in request.data['quyet_toan_tam_ung']:
                quyet_toan_data['phieu_thanh_toan'] = instance.uuid
                quyet_toan_serializer = QuyetToanCacLanTamUngSerializer(
                    data=quyet_toan_data,
                    context=self.get_serializer_context(),
                )
                quyet_toan_serializer.is_valid(raise_exception=True)
                quyet_toan_serializer.save()

        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )
    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(
            instance, data=request.data, partial=partial
        )
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        # Process chi_tiet_phieu_thanh_toan if provided
        if 'chi_tiet_phieu_thanh_toan' in request.data and isinstance(
            request.data['chi_tiet_phieu_thanh_toan'], list
        ):
            # Get existing chi_tiet_phieu_thanh_toan
            existing_chi_tiet = self.chi_tiet_service.list(
                entity_slug=self.kwargs['entity_slug'],
                user_model=self.request.user,
                phieu_thanh_toan_uuid=instance.uuid,
            )

            # Delete existing chi_tiet_phieu_thanh_toan
            for chi_tiet in existing_chi_tiet:
                self.chi_tiet_service.delete(
                    entity_slug=self.kwargs['entity_slug'],
                    user_model=self.request.user,
                    instance=chi_tiet,
                )

            # Create new chi_tiet_phieu_thanh_toan
            for chi_tiet_data in request.data['chi_tiet_phieu_thanh_toan']:
                chi_tiet_data['phieu_thanh_toan'] = instance.uuid
                chi_tiet_serializer = ChiTietPhieuThanhToanTamUngSerializer(
                    data=chi_tiet_data,
                    context=self.get_serializer_context(),
                )
                chi_tiet_serializer.is_valid(raise_exception=True)
                chi_tiet_serializer.save()

        # Process thue_phieu_thanh_toan if provided
        if 'thue_phieu_thanh_toan' in request.data and isinstance(
            request.data['thue_phieu_thanh_toan'], list
        ):
            # Get existing thue_phieu_thanh_toan
            existing_thue = self.thue_service.list(
                entity_slug=self.kwargs['entity_slug'],
                user_model=self.request.user,
                phieu_thanh_toan_uuid=instance.uuid,
            )

            # Delete existing thue_phieu_thanh_toan
            for thue in existing_thue:
                self.thue_service.delete(
                    entity_slug=self.kwargs['entity_slug'],
                    user_model=self.request.user,
                    instance=thue,
                )

            # Create new thue_phieu_thanh_toan
            for thue_data in request.data['thue_phieu_thanh_toan']:
                thue_data['phieu_thanh_toan'] = instance.uuid
                thue_serializer = ThuePhieuThanhToanTamUngSerializer(
                    data=thue_data, context=self.get_serializer_context()
                )
                thue_serializer.is_valid(raise_exception=True)
                thue_serializer.save()

        # Process quyet_toan_tam_ung if provided
        if 'quyet_toan_tam_ung' in request.data and isinstance(
            request.data['quyet_toan_tam_ung'], list
        ):
            # Get existing quyet_toan_tam_ung
            existing_quyet_toan = self.quyet_toan_service.list(
                entity_slug=self.kwargs['entity_slug'],
                user_model=self.request.user,
                phieu_thanh_toan_uuid=instance.uuid,
            )

            # Delete existing quyet_toan_tam_ung
            for quyet_toan in existing_quyet_toan:
                self.quyet_toan_service.delete(
                    entity_slug=self.kwargs['entity_slug'],
                    user_model=self.request.user,
                    instance=quyet_toan,
                )

            # Create new quyet_toan_tam_ung
            for quyet_toan_data in request.data['quyet_toan_tam_ung']:
                quyet_toan_data['phieu_thanh_toan'] = instance.uuid
                quyet_toan_serializer = QuyetToanCacLanTamUngSerializer(
                    data=quyet_toan_data,
                    context=self.get_serializer_context(),
                )
                quyet_toan_serializer.is_valid(raise_exception=True)
                quyet_toan_serializer.save()

        return Response(serializer.data)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        instance = self.get_object()
        # Delete related chi_tiet_phieu_thanh_toan
        existing_chi_tiet = self.chi_tiet_service.list(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
            phieu_thanh_toan_uuid=instance.uuid,
        )
        for chi_tiet in existing_chi_tiet:
            self.chi_tiet_service.delete(
                entity_slug=self.kwargs['entity_slug'],
                user_model=self.request.user,
                instance=chi_tiet,
            )

        # Delete related thue_phieu_thanh_toan
        existing_thue = self.thue_service.list(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
            phieu_thanh_toan_uuid=instance.uuid,
        )
        for thue in existing_thue:
            self.thue_service.delete(
                entity_slug=self.kwargs['entity_slug'],
                user_model=self.request.user,
                instance=thue,
            )

        # Delete related quyet_toan_tam_ung
        existing_quyet_toan = self.quyet_toan_service.list(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
            phieu_thanh_toan_uuid=instance.uuid,
        )
        for quyet_toan in existing_quyet_toan:
            self.quyet_toan_service.delete(
                entity_slug=self.kwargs['entity_slug'],
                user_model=self.request.user,
                instance=quyet_toan,
            )

        # Delete the main instance
        self.service.delete(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
            instance=instance,
        )

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['get'])
    def chi_tiet(self, request, *args, **kwargs):  # noqa: C901
        """
        Get the chi_tiet_phieu_thanh_toan for a PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        instance = self.get_object()
        chi_tiet = self.chi_tiet_service.list(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
            phieu_thanh_toan_uuid=instance.uuid,
        )

        serializer = ChiTietPhieuThanhToanTamUngSerializer(
            chi_tiet, many=True, context=self.get_serializer_context()
        )

        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def thue(self, request, *args, **kwargs):  # noqa: C901
        """
        Get the thue_phieu_thanh_toan for a PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        instance = self.get_object()
        thue = self.thue_service.list(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
            phieu_thanh_toan_uuid=instance.uuid,
        )

        serializer = ThuePhieuThanhToanTamUngSerializer(
            thue, many=True, context=self.get_serializer_context()
        )

        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def quyet_toan(self, request, *args, **kwargs):  # noqa: C901
        """
        Get the quyet_toan_tam_ung for a PhieuThanhToanTamUngModel instance.

        Parameters
        ----------
        request: Request
            The HTTP request.
        args
            Additional arguments.
        kwargs
            Additional keyword arguments.

        Returns
        -------
        Response
            The HTTP response.
        """
        instance = self.get_object()
        quyet_toan = self.quyet_toan_service.list(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
            phieu_thanh_toan_uuid=instance.uuid,
        )

        serializer = QuyetToanCacLanTamUngSerializer(
            quyet_toan, many=True, context=self.get_serializer_context()
        )

        return Response(serializer.data)
