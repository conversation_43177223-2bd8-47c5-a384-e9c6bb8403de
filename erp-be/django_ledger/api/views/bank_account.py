"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for the BankAccountModel.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import status  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401,

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.bank_account import (  # noqa: F401,
    BankAccountModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models import BankAccountModel  # noqa: F401,
from django_ledger.services.bank_account.service import (  # noqa: F401,
    BankAccountModelService,
)


@extend_schema_view(
    list=extend_schema(
        summary='List Bank Accounts',
        description='Returns a list of bank accounts for a specific entity',
        tags=['Bank Accounts'],
    ),
    retrieve=extend_schema(
        summary='Retrieve Bank Account',
        description='Retrieves a specific bank account by UUID',
        tags=['Bank Accounts'],
    ),
    create=extend_schema(
        summary='Create Bank Account',
        description='Creates a new bank account for a specific entity',
        tags=['Bank Accounts'],
    ),
    update=extend_schema(
        summary='Update Bank Account',
        description='Updates a specific bank account by UUID',
        tags=['Bank Accounts'],
    ),
    partial_update=extend_schema(
        summary='Partially Update Bank Account',
        description='Partially updates a specific bank account by UUID',
        tags=['Bank Accounts'],
    ),
    destroy=extend_schema(
        summary='Delete Bank Account',
        description='Deletes a specific bank account by UUID',
        tags=['Bank Accounts'],
    ),
)
class BankAccountViewSet(EntityRelatedViewSet):
    """
    ViewSet for managing bank accounts. 

    This viewset provides CRUD operations for bank accounts and follows the service pattern  # noqa: E501
    to handle business logic. It properly processes entity_model from entity_slug and
    provides nested responses for reference fields.
    """

    queryset = BankAccountModel.objects.all()
    serializer_class = BankAccountModelSerializer  # noqa: F811
    lookup_field = 'uuid'
    permission_classes = [IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811

    def get_service(self):  # noqa: C901
        """
        Get the service instance for bank account operations
        """
        return BankAccountModelService(
            entity_slug=self.kwargs['entity_slug'],
            user_model=self.request.user,
        )

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset of bank accounts for the entity.
        """
        entity_slug = self.kwargs['entity_slug']
        return self.queryset.for_entity(
            entity_slug=entity_slug, user_model=self.request.user
        )
    @api_exception_handler
    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List all bank accounts for the entity
        """
        try:
            service = self.get_service()
            entity_slug = self.kwargs['entity_slug']
            # Get pagination parameters
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            # Get all bank accounts
            bank_accounts, pagination_info = service.get_all_bank_accounts(
                entity_slug=entity_slug,
                user_model=self.request.user,
                page=page,
                page_size=page_size,
            )

            serializer = self.get_serializer(bank_accounts, many=True)
            return Response(
                {
                    'count': pagination_info['count'],
                    'next': pagination_info['next'],
                    'previous': pagination_info['previous'],
                    'results': serializer.data,
                }
            )
        except ValueError as e:
            return Response(
                {'error': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new bank account for the entity
        """
        try:
            service = self.get_service()
            entity_slug = self.kwargs['entity_slug']
            data = request.data.copy()
            bank_account = service.create_bank_account(entity_slug, data)
            serializer = self.get_serializer(bank_account)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except ValueError as e:
            return Response(
                {'error': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing bank account for the entity
        """
        try:
            service = self.get_service()
            entity_slug = self.kwargs['entity_slug']
            data = request.data.copy()
            bank_account = service.update_bank_account(
                entity_slug, kwargs['uuid'], data
            )
            serializer = self.get_serializer(bank_account)
            return Response(serializer.data)
        except ValueError as e:
            return Response(
                {'error': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a bank account for the entity
        """
        try:
            service = self.get_service()
            entity_slug = self.kwargs['entity_slug']
            success = service.delete_bank_account(entity_slug, kwargs['uuid'])
            if success:
                return Response(status=status.HTTP_204_NO_CONTENT)
            return Response(
                {'error': 'Bank account not found'},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValueError as e:
            return Response(
                {'error': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def mark_as_active(self, request, **kwargs):  # noqa: C901
        """
        Mark a bank account as active
        """
        try:
            service = self.get_service()
            entity_slug = self.kwargs['entity_slug']
            bank_account = service.mark_as_active(entity_slug, kwargs['uuid'])
            if bank_account:
                serializer = self.get_serializer(bank_account)
                return Response(serializer.data)
            return Response(
                {'error': 'Bank account not found'},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValueError as e:
            return Response(
                {'error': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def mark_as_inactive(self, request, **kwargs):  # noqa: C901
        """
        Mark a bank account as inactive
        """
        try:
            service = self.get_service()
            entity_slug = self.kwargs['entity_slug']
            bank_account = service.mark_as_inactive(entity_slug, kwargs['uuid'])
            if bank_account:
                serializer = self.get_serializer(bank_account)
                return Response(serializer.data)
            return Response(
                {'error': 'Bank account not found'},
                status=status.HTTP_404_NOT_FOUND,
            )
        except ValueError as e:
            return Response(
                {'error': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
