"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

View for ChiTietHoaDon model.
"""

from django.utils.translation import gettext_lazy as _
from rest_framework import status, viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from django_ledger.api.serializers.ban_hang.hoa_don_ban_ra.hoa_don_dich_vu import (  # noqa: E402
    ChiTietHoaDonSerializer,
    ChiTietHoaDonNestedSerializer,
)
from django_ledger.services.ban_hang.hoa_don_ban_ra import (  # noqa: E402
    ChiTietHoaDonService,
)


class ChiTietHoaDonViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ChiTietHoaDonModel.
    """

    serializer_class = ChiTietHoaDonSerializer  # noqa: F811
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'put', 'delete']

    def get_serializer_class(self):
        """
        Return the appropriate serializer class based on the action.
        Use nested serializer for list/retrieve to include related data.
        """
        if self.action in ['list', 'retrieve']:
            return ChiTietHoaDonNestedSerializer
        return ChiTietHoaDonSerializer

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for ChiTietHoaDonModel.
        Filters by entity_slug and hoa_don_id if provided in the URL.
        """
        entity_slug = self.kwargs.get('entity_slug')
        hoa_don_id = self.kwargs.get('hoa_don_id')
        user_model = self.request.user
        service = ChiTietHoaDonService(
            entity_slug=entity_slug, user_model=user_model
        )
        if hoa_don_id:
            return service.get_for_hoa_don(hoa_don_uuid=hoa_don_id)
        return service.get_queryset()

    def get_serializer_context(self):  # noqa: C901
        """
        Add entity_slug and hoa_don_id to serializer context.
        """
        context = super().get_serializer_context()
        context['entity_slug'] = self.kwargs.get('entity_slug')
        context['hoa_don_id'] = self.kwargs.get('hoa_don_id')
        return context

    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new ChiTietHoaDonModel.
        """
        hoa_don_id = self.kwargs.get('hoa_don_id')
        if not hoa_don_id:
            return Response(
                {"error": _("hoa_don_id is required")},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Add hoa_don to request data
        data = request.data.copy()
        data['hoa_don'] = hoa_don_id
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing ChiTietHoaDonModel.
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(
            instance, data=request.data, partial=partial
        )
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a ChiTietHoaDonModel.
        """
        instance = self.get_object()
        entity_slug = self.kwargs.get('entity_slug')
        user_model = self.request.user
        service = ChiTietHoaDonService(
            entity_slug=entity_slug, user_model=user_model
        )
        service.delete(uuid=instance.uuid)
        return Response(status=status.HTTP_204_NO_CONTENT)
