"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for So Chi Tiet Cong No Nhieu Khach Hang API.
"""

import time
from typing import Dict, Any, List
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.request import Request
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from django_ledger.api.serializers.ban_hang.cong_no_khach_hang.so_chi_tiet_cong_no_nhieu_khach_hang import (
    SoChiTietCongNoNhieuKhachHangRequestSerializer,
    SoChiTietCongNoNhieuKhachHangResponseSerializer
)
from django_ledger.services.ban_hang.cong_no_khach_hang.so_chi_tiet_cong_no_nhieu_khach_hang import (
    SoChiTietCongNoNhieuKhachHangService
)
from django_ledger.api.views.common import ERPPagination
from django_ledger.api.decorators import api_exception_handler
from rest_framework import permissions
from drf_spectacular.utils import extend_schema, extend_schema_view


@extend_schema_view(
    list=extend_schema(
        summary="Get Customer Debt Detail Report (Multiple Customers)",
        description="Get customer debt detail report (So Chi Tiet Cong No Nhieu Khach Hang) with filtering and pagination via POST body data",
        request=SoChiTietCongNoNhieuKhachHangRequestSerializer,
        responses={200: SoChiTietCongNoNhieuKhachHangResponseSerializer(many=True)},
    )
)
class SoChiTietCongNoNhieuKhachHangViewSet(viewsets.ViewSet):
    """
    ViewSet for So Chi Tiet Cong No Nhieu Khach Hang API.

    Provides endpoint for generating customer debt detail reports for multiple customers.
    Uses standard DRF pagination format like Bang Can Doi Phat Sinh Cong No.
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination

    def __init__(self, **kwargs):
        """Initialize viewset with lazy service loading."""
        super().__init__(**kwargs)
        self.service = None

    @api_exception_handler
    def get_report(self, request, entity_slug):
        """
        Generate customer debt detail report with the provided filters.

        This endpoint accepts filter parameters via POST body data and returns
        a paginated list of customer debt detail records using standard DRF format.

        Parameters
        ----------
        request : Request
            The HTTP request object containing filter parameters in body
        entity_slug : str
            The entity slug from URL path

        Returns
        -------
        Response
            The customer debt detail report data with pagination
        """
        # Validate POST body data
        serializer = SoChiTietCongNoNhieuKhachHangRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                {"detail": "Invalid parameters", "errors": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get validated data
        validated_data = serializer.validated_data

        # Initialize service if not already done
        if self.service is None:
            self.service = SoChiTietCongNoNhieuKhachHangService()

        # Generate report using service
        report_data = self.service.generate_report(
            entity_slug=entity_slug, filters=validated_data
        )

        # Apply pagination
        paginator = self.pagination_class()
        paginated_data = paginator.paginate_queryset(report_data, request)

        # Serialize response data
        response_serializer = SoChiTietCongNoNhieuKhachHangResponseSerializer(
            paginated_data, many=True
        )

        # Return paginated response using standard DRF format
        return paginator.get_paginated_response(response_serializer.data)


