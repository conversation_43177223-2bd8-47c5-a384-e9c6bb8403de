"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for Bang Xac Nhan Cong No (Debt Confirmation Report) API.
"""

from django_ledger.api.serializers.ban_hang.cong_no_khach_hang.bang_xac_nhan_cong_no import (
    BangXacNhanCongNoRequestSerializer,
    BangXacNhanCongNoResponseSerializer,
)
from django_ledger.api.views.common import ERPPagination
from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import status, viewsets, permissions
from rest_framework.response import Response
from django_ledger.api.decorators import api_exception_handler


@extend_schema_view(
    list=extend_schema(
        summary="Get Debt Confirmation Report",
        description="Get debt confirmation report (Bang Xac Nhan Cong No) with filtering and pagination via POST body data",
        request=BangXacNhanCongNoRequestSerializer,
        responses={200: BangXacNhanCongNoResponseSerializer(many=True)},
    )
)
class BangXacNhanCongNoViewSet(viewsets.ViewSet):
    """
    ViewSet for handling Debt Confirmation Report (Bang Xac Nhan Cong No) API requests.

    This viewset provides endpoints for generating debt confirmation reports with comprehensive filtering
    and pagination support. All filters are passed via POST body data.
    
    The report shows transaction-level details for debt confirmation with customers,
    including corresponding accounts and document references.
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = None

    @api_exception_handler
    def get_report(self, request, entity_slug):
        """
        Generate debt confirmation report with the provided filters.

        This endpoint accepts filter parameters via POST body data and returns
        a paginated list of debt confirmation transaction records.

        Parameters
        ----------
        request : Request
            The HTTP request object containing filter parameters in body
        entity_slug : str
            The entity slug from URL path

        Returns
        -------
        Response
            The debt confirmation report data with pagination
        """
        # Validate POST body data
        serializer = BangXacNhanCongNoRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                {"detail": "Invalid parameters", "errors": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get validated data
        validated_data = serializer.validated_data

        # Initialize service if not already done
        if self.service is None:
            from django_ledger.services.ban_hang.cong_no_khach_hang.bang_xac_nhan_cong_no import (
                BangXacNhanCongNoService,
            )

            self.service = BangXacNhanCongNoService()

        # Generate debt confirmation report
        report_data = self.service.generate_report(
            entity_slug=entity_slug, filters=validated_data
        )

        # Apply pagination
        paginator = self.pagination_class()
        paginated_data = paginator.paginate_queryset(report_data, request)

        # Serialize response data
        response_serializer = BangXacNhanCongNoResponseSerializer(
            paginated_data, many=True
        )

        # Return paginated response
        return paginator.get_paginated_response(response_serializer.data)
