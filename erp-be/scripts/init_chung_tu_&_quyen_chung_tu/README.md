# Khởi tạo Chứng từ & Quyển chứng từ ERP

Script tự động thiết lập chứng từ và quyển chứng từ cho hệ thống ERP.

## 🚀 Cách sử dụng

### Thiết lập biến môi trường

Trước khi chạy script, cần thiết lập thông tin xác thực:

```bash
# Cách 1: Export biến môi trường
export ERP_USERNAME='your_username'
export ERP_PASSWORD='your_password'
./setup_new_database_erp.sh
```

```bash
# Cách 2: Chạy trực tiếp với biến môi trường
ERP_USERNAME='your_username' ERP_PASSWORD='your_password' ./setup_new_database_erp.sh
```

### Chạy script chính

```bash
./setup_new_database_erp.sh
```

## 📋 Quy trình thực hiện

Script sẽ tự động thực hiện các bước sau:

### 🔍 Bước 0: Kiểm tra điều kiện tiên quyết
- Kiểm tra sự tồn tại của file `create_all_chung_tu.sh`
- Kiểm tra sự tồn tại của file `create_all_quyen_chung_tu.sh`
- Báo lỗi và dừng nếu thiếu file cần thiết

### 🔥 Bước 1: Tạo chứng từ
- Thực thi script `./create_all_chung_tu.sh`
- Tạo **48 chứng từ** với mã định danh đơn giản

### 🔥 Bước 2: Tạo quyển chứng từ
- Thực thi script `./create_all_quyen_chung_tu.sh`
- Tạo **21 quyển chứng từ**

### 🔧 Bước 3: Sửa lỗi UUID và thêm chi tiết
- Tự động tạo script sửa lỗi nếu cần thiết
- Lấy UUID thực tế từ database
- Thêm chi tiết quyển với UUID chính xác
- Xử lý tất cả các nhóm chứng từ:
  - `TIỀN_MẶT`
  - `TIỀN_GỬI`
  - `BÁN_HÀNG`
  - `HÓA_ĐƠN`
  - `MUA_HÀNG`
  - `TỒN_KHO`
  - `GIÁ_THÀNH`

### ✅ Bước 4: Kiểm tra kết quả
- Đếm số lượng chứng từ và quyển đã tạo
- Báo cáo kết quả thành công

## 🌟 Ưu điểm

### 🛡️ An toàn và ổn định
- Kiểm tra file cần thiết trước khi thực thi
- Xử lý lỗi tự động
- Không bị crash khi gặp vấn đề

### 🔄 Tự động sửa lỗi
- Tự động tạo script sửa lỗi UUID
- Tìm UUID thực tế từ database
- Không cần hardcode UUID

### 📊 Báo cáo chi tiết
- Hiển thị tiến độ từng bước
- Đếm số lượng thành công
- Hướng dẫn sử dụng sau khi setup

## 🎯 Kết quả

Sau khi chạy thành công, database sẽ có:

- ✅ **48+ chứng từ** sẵn sàng sử dụng
- ✅ **21+ quyển chứng từ** đã được cấu hình
- ✅ **Chi tiết quyển** đã được liên kết
- ✅ **Format tự động**: `PT1.[MM].[YY].[######]`

## 📝 Lưu ý

### Bảo mật
- **Không bao giờ** hardcode username/password trong script
- Sử dụng biến môi trường `ERP_USERNAME` và `ERP_PASSWORD`
- Không commit thông tin xác thực vào repository

### Yêu cầu hệ thống
- Đảm bảo database đã được thiết lập trước khi chạy script
- Script cần quyền thực thi trên các file `.sh`
- Kiểm tra kết nối database trước khi bắt đầu
- Server ERP phải đang chạy tại `http://localhost:8000`

### Xử lý lỗi
- Nếu thiếu biến môi trường, script sẽ dừng và hiển thị hướng dẫn
- Nếu xác thực thất bại, kiểm tra lại username/password
- Script có cơ chế tự động sửa lỗi UUID nếu cần thiết