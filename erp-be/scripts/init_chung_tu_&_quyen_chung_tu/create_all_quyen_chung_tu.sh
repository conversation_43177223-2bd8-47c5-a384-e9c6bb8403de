#!/bin/bash

# Script tạo tất cả quyển chứng từ và chi tiết cho hệ thống ERP
# Tổng hợp từ quyen_chung_tu_curl.md và quyen_chung_tu_chi_tiet_curl.md

set -e  # Exit on any error

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Cấu hình
SERVER="http://localhost:8000"
ENTITY_SLUG="tutimi-dnus2xnc"

# Lấy thông tin xác thực từ biến môi trường
USERNAME="${ERP_USERNAME:-}"
PASSWORD="${ERP_PASSWORD:-}"

# Kiểm tra biến môi trường
if [ -z "$USERNAME" ] || [ -z "$PASSWORD" ]; then
    echo -e "${RED}❌ Thiếu thông tin xác thực!${NC}"
    echo -e "${YELLOW}Vui lòng thiết lập biến môi trường:${NC}"
    echo -e "  export ERP_USERNAME='your_username'"
    echo -e "  export ERP_PASSWORD='your_password'"
    echo ""
    echo -e "${YELLOW}Hoặc chạy với:${NC}"
    echo -e "  ERP_USERNAME='your_username' ERP_PASSWORD='your_password' $0"
    exit 1
fi

# Kiểm tra token
if [ -z "$TOKEN" ]; then
    echo -e "${YELLOW}Lấy token xác thực...${NC}"
    TOKEN_RESPONSE=$(curl -s -X POST "$SERVER/api/auth/token/" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")

    TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)

    if [ -z "$TOKEN" ]; then
        echo -e "${RED}❌ Không thể lấy token. Kiểm tra username/password.${NC}"
        echo "Response: $TOKEN_RESPONSE"
        exit 1
    fi

    echo -e "${GREEN}✅ Đã lấy token thành công${NC}"
fi

# Function để tạo quyển chứng từ
create_quyen_chung_tu() {
    local uuid=$1
    local ma_nk=$2
    local ten_nk=$3
    local ten_nk2=$4
    local so_ct_mau=$5

    echo -e "${BLUE}Tạo quyển: $ma_nk - $ten_nk${NC}"

    RESPONSE=$(curl -s -X POST "$SERVER/api/entities/$ENTITY_SLUG/erp/document-books/" \
        -H "Content-Type: application/json" \
        -H "Authorization: Token $TOKEN" \
        -d "{
            \"uuid\": \"$uuid\",
            \"ma_nk\": \"$ma_nk\",
            \"ten_nk\": \"$ten_nk\",
            \"ten_nk2\": \"$ten_nk2\",
            \"so_ct_mau\": \"$so_ct_mau\",
            \"number_text\": \"$so_ct_mau\",
            \"kieu_trung_so\": \"2\",
            \"i_so_ct_ht\": 1,
            \"ngay_hl1\": \"2024-01-01\",
            \"ngay_hl2\": \"2025-12-31\",
            \"so_ct2\": \"$so_ct_mau\",
            \"status\": \"1\"
        }")

    if echo "$RESPONSE" | grep -q "\"uuid\""; then
        echo -e "${GREEN}  ✅ Thành công: $ma_nk${NC}"
    else
        echo -e "${RED}  ❌ Lỗi: $ma_nk${NC}"
        echo "  Response: $RESPONSE"
    fi
}

# Function để thêm chi tiết vào quyển với UUID
add_chi_tiet() {
    local quyen_uuid=$1
    local chung_tu_uuid=$2
    local line=$3
    local ma_ct_name=$4

    echo -e "${BLUE}  Thêm chi tiết: $ma_ct_name (line $line)${NC}"

    RESPONSE=$(curl -s -X POST "$SERVER/api/entities/$ENTITY_SLUG/erp/document-books/$quyen_uuid/add-document-type/" \
        -H "Content-Type: application/json" \
        -H "Authorization: Token $TOKEN" \
        -d "{
            \"ma_ct\": \"$chung_tu_uuid\",
            \"line\": $line,
            \"username\": \"$USERNAME\"
        }")

    if echo "$RESPONSE" | grep -q "\"uuid\""; then
        echo -e "${GREEN}    ✅ Thành công: $ma_ct_name${NC}"
    else
        echo -e "${RED}    ❌ Lỗi: $ma_ct_name${NC}"
        echo "    Response: $RESPONSE"
    fi
}

echo -e "${YELLOW}🚀 Bắt đầu tạo quyển chứng từ và chi tiết...${NC}"
echo ""

# BƯỚC 1: TẠO QUYỂN CHỨNG TỪ
echo -e "${YELLOW}📚 BƯỚC 1: TẠO QUYỂN CHỨNG TỪ${NC}"
echo ""

# 1. TIỀN MẶT
echo -e "${YELLOW}📁 1. TIỀN MẶT${NC}"
create_quyen_chung_tu "61341a37-fa26-5642-bc07-1829f22204c2" "DNC" "Quyển đề nghị thanh toán" "Payment Proposal Book" "DNC.[MM].[YY].[######]"
create_quyen_chung_tu "505b18d0-87e0-50c6-9196-7f0f838196b6" "PC1" "Quyển phiếu chi tiền" "Cash Payment Voucher Book" "PC1.[MM].[YY].[######]"
create_quyen_chung_tu "a1ce1dc7-5be3-558c-b0f5-006aa6dfbbce" "PT1" "Quyển phiếu thu tiền" "Cash Receipt Voucher Book" "PT1.[MM].[YY].[######]"
echo ""

# 2. TIỀN GỬI
echo -e "${YELLOW}📁 2. TIỀN GỬI${NC}"
create_quyen_chung_tu "33e401ed-c293-53b0-8e22-d5185c304987" "BC1" "Quyển giấy báo có 1" "Credit Advice Book 1" "BC1.[MM].[YY].[######]"
create_quyen_chung_tu "cbb249c3-54f2-5fcf-94e8-f93214a78c5a" "BN1" "Quyển giấy báo nợ" "Debit Advice Book" "BN1.[MM].[YY].[######]"
create_quyen_chung_tu "ddfdd702-7b8f-53f5-bd63-9234c39ac92d" "DNC_TG" "Quyển đề nghị thanh toán ngân hàng" "Bank Payment Proposal Book" "DNC.[MM].[YY].[######]"
echo ""

# 3. BÁN HÀNG
echo -e "${YELLOW}📁 3. BÁN HÀNG${NC}"
create_quyen_chung_tu "1e8ceeb5-a0f8-5b3b-8e2d-f81c2a650154" "BG1" "Quyển báo giá" "Quotation Book" "BG1.[MM].[YY].[######]"
create_quyen_chung_tu "8b496141-408d-5c5e-ad1b-5224af93fad0" "DH1" "Quyển đơn hàng" "Sales Order Book" "DH1.[MM].[YY].[######]"
create_quyen_chung_tu "fae8fa5d-1bd9-5600-a7f5-adcf4233500b" "HD1" "Quyển hóa đơn bán hàng" "Sales Invoice Book" "HD1.[MM].[YY].[######]"
create_quyen_chung_tu "fb83eeea-2736-5943-96d5-13ea7bdc4adc" "DHO" "Quyển đơn hàng online" "Online Order Book" "DHO.[MM].[YY].[######]"
create_quyen_chung_tu "a033f1c9-2613-50f0-ad61-addf2e8332de" "HD2" "Quyển hóa đơn bán lẻ" "Retail Invoice Book" "HD2.[MM].[YY].[######]"
create_quyen_chung_tu "846ea003-22ef-538d-88fd-27f41e75ba9b" "HD3" "Quyển hóa đơn xuất khẩu" "Export Invoice Book" "HD3.[MM].[YY].[######]"
create_quyen_chung_tu "bf802aa1-ed16-5aa1-bb62-3786e411c920" "PX3" "Quyển phiếu xuất bán hàng" "Sales Issue Book" "PX3.[MM].[YY].[######]"
create_quyen_chung_tu "f2d2c6d8-7714-5c22-b6d5-5cadeee2d1cb" "TL1" "Quyển phiếu nhập trả lại" "Return Receipt Book" "TL1.[MM].[YY].[######]"
create_quyen_chung_tu "91305a96-ba91-5773-bd64-24097f444087" "TL2" "Quyển hóa đơn trả lại" "Return Invoice Book" "TL2.[MM].[YY].[######]"
create_quyen_chung_tu "892cdabe-5cbe-5078-bf42-cf4aefad564b" "TL3" "Quyển hóa đơn giảm giá" "Discount Invoice Book" "TL3.[MM].[YY].[######]"
create_quyen_chung_tu "3ffdc290-8c70-5256-bc5b-1d650e4da99a" "TL4" "Quyển hóa đơn điều chỉnh tăng" "Adjustment Increase Invoice Book" "TL4.[MM].[YY].[######]"
echo ""

# 4. HÓA ĐƠN
echo -e "${YELLOW}📁 4. HÓA ĐƠN${NC}"
create_quyen_chung_tu "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "HD_GTGT" "Quyển hóa đơn GTGT" "VAT Invoice Book" "HD.[MM].[YY].[######]"
echo ""

# 5. MUA HÀNG
echo -e "${YELLOW}📁 5. MUA HÀNG${NC}"
create_quyen_chung_tu "f56f69f6-ba75-550b-bd3a-46fb67924914" "MH_DH" "Quyển đơn hàng mua" "Purchase Order Book" "DH.[MM].[YY].[######]"
echo ""

# 6. TỒN KHO
echo -e "${YELLOW}📁 6. TỒN KHO${NC}"
create_quyen_chung_tu "7256281c-2ed4-5cb7-905b-8af16c78aefd" "TK_PN" "Quyển phiếu nhập kho" "Goods Receipt Book" "PN.[MM].[YY].[######]"
echo ""

# 7. GIÁ THÀNH
echo -e "${YELLOW}📁 7. GIÁ THÀNH${NC}"
create_quyen_chung_tu "64fa4b4d-a390-5c06-8cd2-9d408be02474" "GT_DM" "Quyển định mức" "Standard Book" "DM.[MM].[YY].[######]"
echo ""

# BƯỚC 2: THÊM CHI TIẾT VÀO QUYỂN
echo -e "${YELLOW}📝 BƯỚC 2: THÊM CHI TIẾT VÀO QUYỂN${NC}"
echo ""

# 1. TIỀN MẶT
echo -e "${YELLOW}📁 1. CHI TIẾT TIỀN MẶT${NC}"
add_chi_tiet "61341a37-fa26-5642-bc07-1829f22204c2" "013f2847-e3c3-502a-a6fd-4e85e75ce07d" 1 "Đề nghị thanh toán"
add_chi_tiet "505b18d0-87e0-50c6-9196-7f0f838196b6" "819c4a9d-6546-5f4b-bd93-989015a00d83" 1 "Phiếu chi tiền"
add_chi_tiet "a1ce1dc7-5be3-558c-b0f5-006aa6dfbbce" "b01da8f3-aeee-5f92-b9fd-5ea5438fc633" 1 "Phiếu thu tiền"
echo ""

# 2. TIỀN GỬI
echo -e "${YELLOW}📁 2. CHI TIẾT TIỀN GỬI${NC}"
add_chi_tiet "33e401ed-c293-53b0-8e22-d5185c304987" "88a13ee8-e801-59ac-9e08-14922b588f09" 1 "Giấy báo có 1"
add_chi_tiet "cbb249c3-54f2-5fcf-94e8-f93214a78c5a" "81d417a5-9ac3-5534-b18f-17aef6b1f17c" 1 "Giấy báo nợ"
add_chi_tiet "ddfdd702-7b8f-53f5-bd63-9234c39ac92d" "013f2847-e3c3-502a-a6fd-4e85e75ce07d" 1 "Đề nghị thanh toán"
echo ""

# 3. BÁN HÀNG
echo -e "${YELLOW}📁 3. CHI TIẾT BÁN HÀNG${NC}"
add_chi_tiet "1e8ceeb5-a0f8-5b3b-8e2d-f81c2a650154" "b35652a3-135d-5475-ba3f-f224336bce0d" 1 "Báo giá"
add_chi_tiet "8b496141-408d-5c5e-ad1b-5224af93fad0" "3c0b88fe-bcfb-53c7-8eab-20334b97d37f" 1 "Đơn hàng"
add_chi_tiet "fae8fa5d-1bd9-5600-a7f5-adcf4233500b" "37d3e3fa-519a-5b9b-9f02-fedcf3950ed9" 1 "Hóa đơn bán hàng"
add_chi_tiet "fb83eeea-2736-5943-96d5-13ea7bdc4adc" "e5cf682d-2def-595c-b04b-6654502e88a0" 1 "Đơn hàng online"
add_chi_tiet "a033f1c9-2613-50f0-ad61-addf2e8332de" "8b40a8a5-9ff0-53f4-a99d-14fd0a0fdedb" 1 "Hóa đơn bán lẻ"
add_chi_tiet "846ea003-22ef-538d-88fd-27f41e75ba9b" "c2cacc44-abbb-57f6-b762-46d16d5bc26b" 1 "Hóa đơn xuất khẩu"
add_chi_tiet "bf802aa1-ed16-5aa1-bb62-3786e411c920" "1d69d473-1800-50da-8be3-acda17988d9b" 1 "Phiếu xuất bán hàng"
add_chi_tiet "f2d2c6d8-7714-5c22-b6d5-5cadeee2d1cb" "700c8a3c-ad6d-582d-85b9-753ef6f656e1" 1 "Phiếu nhập trả lại"
add_chi_tiet "91305a96-ba91-5773-bd64-24097f444087" "ae932c88-0510-5dfe-9c6e-7282d0b31c38" 1 "Hóa đơn trả lại"
add_chi_tiet "892cdabe-5cbe-5078-bf42-cf4aefad564b" "4a316dce-47c5-599c-8289-e71f2d72cff3" 1 "Hóa đơn giảm giá"
add_chi_tiet "3ffdc290-8c70-5256-bc5b-1d650e4da99a" "5604568d-13af-50d5-9d5d-ffd00bb77196" 1 "Hóa đơn điều chỉnh tăng"
echo ""

# 4. HÓA ĐƠN (tập trung vào 1 quyển)
echo -e "${YELLOW}📁 4. CHI TIẾT HÓA ĐƠN${NC}"
add_chi_tiet "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "37df37d7-8518-55b3-acce-f4f28d501961" 1 "Hóa đơn GTGT"
add_chi_tiet "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "cddaa5a1-4956-5aef-b8e9-df75b0f26d93" 2 "Hóa đơn bán hàng"
add_chi_tiet "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "2c7f5012-0c5c-537e-942f-3aef445d9860" 3 "Hóa đơn thương mại"
add_chi_tiet "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "08c540fc-2e92-503d-908c-04dc6af6df5e" 4 "Hóa đơn điện tử"
add_chi_tiet "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "702e9291-138f-5447-bb4e-1f8011d3d43d" 5 "Hóa đơn xuất khẩu"
add_chi_tiet "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "19d8871a-2888-5bde-a994-bfed2993da81" 6 "Hóa đơn nhập khẩu"
add_chi_tiet "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "0f8d698a-f3bd-51c7-a17c-21cc32ee5bf0" 7 "Hóa đơn dịch vụ"
add_chi_tiet "d388e34b-9b19-56b1-94c9-60b3f8319ef9" "ac117d08-1ec8-57f4-8910-34a9051d2372" 8 "Hóa đơn khác"
echo ""

# 5. MUA HÀNG (tập trung vào 1 quyển)
echo -e "${YELLOW}📁 5. CHI TIẾT MUA HÀNG${NC}"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "07252efa-958a-58a0-b84f-da9b4e25604a" 1 "Chứng từ phải trả khác"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "8a744cbb-304e-5bc2-a8b9-0d51bd89ce2d" 2 "Bút toán điều chỉnh giảm công nợ"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "624f11cd-429c-53dd-b99e-f1efd24ae3d0" 3 "Phiếu nhập chi phí mua hàng"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "b8e7ee42-7974-5835-8167-e0de240f27ca" 4 "Phiếu nhập điều chỉnh giá hàng mua"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "43cb4826-c93e-5ea0-b5ea-60a1a288a342" 5 "Đơn hàng mua trong nước"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "4f59c6a0-dde0-5c36-b750-cd1a4d9f55bd" 6 "Đơn hàng mua nhập khẩu"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "d40a8879-acf6-5df6-bc7e-8d6d7185d49d" 7 "Hóa đơn mua hàng trong nước"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "990cd582-4805-5c16-a222-bafa0e8b28a6" 8 "Hóa đơn mua hàng nhập khẩu"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "c61ad6bc-93a9-5e2f-ac3d-a3f392668bf5" 9 "Hóa đơn mua dịch vụ"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "11e5067e-3aef-520e-acef-d713aeba84bb" 10 "Hóa đơn đầu vào"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "4883b3a1-5d71-504f-90dd-202b100fcf68" 11 "Phiếu xuất trả lại nhà cung cấp"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "496faabf-f9e1-599a-8645-aea2511ac7cb" 12 "Hóa đơn dịch vụ trả lại nhà cung cấp"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "7ba8085f-3916-5a7f-8154-615a60fbb7de" 13 "Phiếu thanh toán tạm ứng"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "4e884757-f5ff-5bbe-861d-9bab93dfcd9c" 14 "Hóa đơn nhập mua - xuất thẳng"
add_chi_tiet "f56f69f6-ba75-550b-bd3a-46fb67924914" "75647034-3ca9-5cf0-be7a-d1d6985df014" 15 "Phiếu xuất thẳng"
echo ""

# 6. TỒN KHO (tập trung vào 1 quyển)
echo -e "${YELLOW}📁 6. CHI TIẾT TỒN KHO${NC}"
add_chi_tiet "7256281c-2ed4-5cb7-905b-8af16c78aefd" "53697c95-1da1-5cc6-8c69-f00d8c9e6e86" 1 "Phiếu yêu cầu kiểm kê"
add_chi_tiet "7256281c-2ed4-5cb7-905b-8af16c78aefd" "599e969f-e259-5d93-99aa-a72d70d2b77b" 2 "Phiếu nhập kho"
add_chi_tiet "7256281c-2ed4-5cb7-905b-8af16c78aefd" "9c2571b3-85b5-53cd-ba38-19d85e4a0b96" 3 "Phiếu nhập điều chuyển"
add_chi_tiet "7256281c-2ed4-5cb7-905b-8af16c78aefd" "4825a41d-7f34-5a31-8f19-a9beed8e5a0e" 4 "Phiếu nhập xuất thẳng"
add_chi_tiet "7256281c-2ed4-5cb7-905b-8af16c78aefd" "ac9123d8-3538-5c56-b531-46523af219b8" 5 "Phiếu xuất kho"
add_chi_tiet "7256281c-2ed4-5cb7-905b-8af16c78aefd" "PX2" 6 "Phiếu xuất điều chuyển"
add_chi_tiet "7256281c-2ed4-5cb7-905b-8af16c78aefd" "68357b30-19e0-5954-b8c3-d9ad5aaf9aee" 7 "Phiếu yêu cầu xuất kho"
echo ""

# 7. GIÁ THÀNH (tập trung vào 1 quyển)
echo -e "${YELLOW}📁 7. CHI TIẾT GIÁ THÀNH${NC}"
add_chi_tiet "64fa4b4d-a390-5c06-8cd2-9d408be02474" "6728eea4-1261-568d-931f-855be629e4c5" 1 "Định mức nguyên vật liệu"
add_chi_tiet "64fa4b4d-a390-5c06-8cd2-9d408be02474" "4471ccf2-91d6-5220-a9e2-57928df3b7f8" 2 "Lệnh sản xuất"
echo ""

echo -e "${GREEN}🎉 Hoàn thành! Đã tạo tất cả quyển chứng từ và chi tiết.${NC}"
echo ""
echo -e "${BLUE}📊 Thống kê:${NC}"
echo -e "  • Quyển chứng từ: 17 quyển"
echo -e "  • Chi tiết quyển: 49 chi tiết"
echo -e "  ${GREEN}Hệ thống ERP đã sẵn sàng sử dụng!${NC}"
