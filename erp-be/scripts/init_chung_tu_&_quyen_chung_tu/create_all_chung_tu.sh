#!/bin/bash

# Script tạo tất cả chứng từ (<PERSON><PERSON><PERSON>) cho hệ thống ERP
# Tổng hợp từ 7 file curl_commands_*.md

set -e  # Exit on any error

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Cấu hình
SERVER="http://localhost:8000"
ENTITY_SLUG="tutimi-dnus2xnc"

# Lấy thông tin xác thực từ biến môi trường
USERNAME="${ERP_USERNAME:-}"
PASSWORD="${ERP_PASSWORD:-}"

# Kiểm tra biến môi trường
if [ -z "$USERNAME" ] || [ -z "$PASSWORD" ]; then
    echo -e "${RED}❌ Thiếu thông tin xác thực!${NC}"
    echo -e "${YELLOW}Vui lòng thiết lập biến môi trường:${NC}"
    echo -e "  export ERP_USERNAME='your_username'"
    echo -e "  export ERP_PASSWORD='your_password'"
    echo ""
    echo -e "${YELLOW}Hoặc chạy với:${NC}"
    echo -e "  ERP_USERNAME='your_username' ERP_PASSWORD='your_password' $0"
    exit 1
fi

# Kiểm tra token
if [ -z "$TOKEN" ]; then
    echo -e "${YELLOW}Lấy token xác thực...${NC}"
    TOKEN_RESPONSE=$(curl -s -X POST "$SERVER/api/auth/token/" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")

    TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)

    if [ -z "$TOKEN" ]; then
        echo -e "${RED}❌ Không thể lấy token. Kiểm tra username/password.${NC}"
        echo "Response: $TOKEN_RESPONSE"
        exit 1
    fi

    echo -e "${GREEN}✅ Đã lấy token thành công${NC}"
fi

# Function để tạo chứng từ
create_chung_tu() {
    local uuid=$1
    local ma_ct=$2
    local ten_ct=$3
    local ten_ct2=$4
    local ten_ct3=$5
    local xcode=$6
    local stt=$7
    local ct_kt_ton=$8
    local loai_dl_ton=$9
    local ct_sd_vi_tri=${10}

    echo -e "${BLUE}Tạo chứng từ: $ma_ct - $ten_ct${NC}"

    RESPONSE=$(curl -s -X POST "$SERVER/api/entities/$ENTITY_SLUG/erp/documents/" \
        -H "Content-Type: application/json" \
        -H "Authorization: Token $TOKEN" \
        -d "{
            \"uuid\": \"$uuid\",
            \"ma_ct\": \"$ma_ct\",
            \"ten_ct\": \"$ten_ct\",
            \"ten_ct2\": \"$ten_ct2\",
            \"ten_ct3\": \"$ten_ct3\",
            \"ngay_ks\": \"2024-01-01\",
            \"stt\": $stt,
            \"i_so_ct\": 1000,
            \"d_page_count\": 1,
            \"order_type\": \"0\",
            \"df_status\": \"5\",
            \"ct_kt_ton\": \"$ct_kt_ton\",
            \"loai_dl_ton\": \"$loai_dl_ton\",
            \"ct_sd_vi_tri\": $ct_sd_vi_tri,
            \"ngay_lct_yn\": true,
            \"vc_link\": \"\",
            \"ct_save_log\": \"1\",
            \"xcode\": \"$xcode\",
            \"user_id0_yn\": true,
            \"user_id2_yn\": true
        }")

    if echo "$RESPONSE" | grep -q "\"uuid\""; then
        echo -e "${GREEN}  ✅ Thành công: $ma_ct${NC}"
    else
        echo -e "${RED}  ❌ Lỗi: $ma_ct${NC}"
        echo "  Response: $RESPONSE"
    fi
}

echo -e "${YELLOW}🚀 Bắt đầu tạo tất cả chứng từ cho hệ thống ERP...${NC}"
echo ""

# 1. TIỀN MẶT (xcode=tien_mat)
echo -e "${YELLOW}📁 1. TIỀN MẶT (xcode=tien_mat)${NC}"
create_chung_tu "013f2847-e3c3-502a-a6fd-4e85e75ce07d" "DNC" "Đề nghị thanh toán" "Payment Proposal" "Payment Request" "tien_mat" 1 "0" "0" false
create_chung_tu "819c4a9d-6546-5f4b-bd93-989015a00d83" "PC1" "Phiếu chi tiền" "Cash Payment Voucher" "Cash Payment" "tien_mat" 2 "0" "0" false
create_chung_tu "b01da8f3-aeee-5f92-b9fd-5ea5438fc633" "PT1" "Phiếu thu tiền" "Cash Receipt Voucher" "Cash Receipt" "tien_mat" 3 "0" "0" false
echo ""

# 2. TIỀN GỬI (xcode=tien_gui)
echo -e "${YELLOW}📁 2. TIỀN GỬI (xcode=tien_gui)${NC}"
create_chung_tu "88a13ee8-e801-59ac-9e08-14922b588f09" "BC1" "Giấy báo có 1" "Credit Advice 1" "Credit Note" "tien_gui" 1 "0" "0" false
create_chung_tu "81d417a5-9ac3-5534-b18f-17aef6b1f17c" "BN1" "Giấy báo nợ" "Debit Advice" "Debit Note" "tien_gui" 2 "0" "0" false
create_chung_tu "013f2847-e3c3-502a-a6fd-4e85e75ce07d" "DNC" "Đề nghị thanh toán" "Payment Proposal" "Payment Request" "tien_gui" 3 "0" "0" false
echo ""

# 3. BÁN HÀNG (xcode=ban_hang)
echo -e "${YELLOW}📁 3. BÁN HÀNG (xcode=ban_hang)${NC}"
create_chung_tu "b35652a3-135d-5475-ba3f-f224336bce0d" "BG1" "Báo giá" "Quotation" "Quote" "ban_hang" 1 "1" "1" true
create_chung_tu "3c0b88fe-bcfb-53c7-8eab-20334b97d37f" "DH1" "Đơn hàng" "Sales Order" "Order" "ban_hang" 2 "1" "1" true
create_chung_tu "37d3e3fa-519a-5b9b-9f02-fedcf3950ed9" "HD1" "Hóa đơn bán hàng" "Sales Invoice" "Invoice" "ban_hang" 3 "1" "1" true
create_chung_tu "e5cf682d-2def-595c-b04b-6654502e88a0" "DHO" "Đơn hàng online" "Online Order" "E-Order" "ban_hang" 4 "1" "1" true
create_chung_tu "8b40a8a5-9ff0-53f4-a99d-14fd0a0fdedb" "HD2" "Hóa đơn bán lẻ" "Retail Invoice" "Retail" "ban_hang" 5 "1" "1" true
create_chung_tu "c2cacc44-abbb-57f6-b762-46d16d5bc26b" "HD3" "Hóa đơn xuất khẩu" "Export Invoice" "Export" "ban_hang" 6 "1" "1" true
create_chung_tu "1d69d473-1800-50da-8be3-acda17988d9b" "PX3" "Phiếu xuất bán hàng" "Sales Issue Slip" "Sales Issue" "ban_hang" 7 "1" "1" true
create_chung_tu "700c8a3c-ad6d-582d-85b9-753ef6f656e1" "TL1" "Phiếu nhập trả lại" "Return Receipt" "Return In" "ban_hang" 8 "1" "1" true
create_chung_tu "ae932c88-0510-5dfe-9c6e-7282d0b31c38" "TL2" "Hóa đơn trả lại" "Return Invoice" "Return" "ban_hang" 9 "1" "1" true
create_chung_tu "4a316dce-47c5-599c-8289-e71f2d72cff3" "TL3" "Hóa đơn giảm giá" "Discount Invoice" "Discount" "ban_hang" 10 "1" "1" true
create_chung_tu "5604568d-13af-50d5-9d5d-ffd00bb77196" "TL4" "Hóa đơn điều chỉnh tăng" "Adjustment Increase Invoice" "Adjustment" "ban_hang" 11 "1" "1" true
echo ""

# 4. HÓA ĐƠN (xcode=hoa_don)
echo -e "${YELLOW}📁 4. HÓA ĐƠN (xcode=hoa_don)${NC}"
create_chung_tu "37df37d7-8518-55b3-acce-f4f28d501961" "HD_GTGT" "Hóa đơn GTGT" "VAT Invoice" "VAT" "hoa_don" 1 "1" "1" true
create_chung_tu "cddaa5a1-4956-5aef-b8e9-df75b0f26d93" "HD_BH" "Hóa đơn bán hàng" "Sales Invoice" "Sales" "hoa_don" 2 "1" "1" true
create_chung_tu "2c7f5012-0c5c-537e-942f-3aef445d9860" "HD_TM" "Hóa đơn thương mại" "Commercial Invoice" "Commercial" "hoa_don" 3 "1" "1" true
create_chung_tu "08c540fc-2e92-503d-908c-04dc6af6df5e" "HD_DT" "Hóa đơn điện tử" "Electronic Invoice" "E-Invoice" "hoa_don" 4 "1" "1" true
create_chung_tu "702e9291-138f-5447-bb4e-1f8011d3d43d" "HD_XK" "Hóa đơn xuất khẩu" "Export Invoice" "Export" "hoa_don" 5 "1" "1" true
create_chung_tu "19d8871a-2888-5bde-a994-bfed2993da81" "HD_NK" "Hóa đơn nhập khẩu" "Import Invoice" "Import" "hoa_don" 6 "1" "1" true
create_chung_tu "0f8d698a-f3bd-51c7-a17c-21cc32ee5bf0" "HD_DV" "Hóa đơn dịch vụ" "Service Invoice" "Service" "hoa_don" 7 "0" "0" false
create_chung_tu "ac117d08-1ec8-57f4-8910-34a9051d2372" "HD_KH" "Hóa đơn khác" "Other Invoice" "Other" "hoa_don" 8 "1" "1" false
echo ""

# 5. MUA HÀNG (xcode=mua_hang)
echo -e "${YELLOW}📁 5. MUA HÀNG (xcode=mua_hang)${NC}"
create_chung_tu "07252efa-958a-58a0-b84f-da9b4e25604a" "CN5" "Chứng từ phải trả khác, chứng từ bù trừ công nợ" "Other Payables, Debt Offset Document" "Payables Offset" "mua_hang" 1 "0" "0" false
create_chung_tu "8a744cbb-304e-5bc2-a8b9-0d51bd89ce2d" "CN6" "Bút toán điều chỉnh giảm công nợ" "Debt Reduction Adjustment Entry" "Debt Adjustment" "mua_hang" 2 "0" "0" false
create_chung_tu "624f11cd-429c-53dd-b99e-f1efd24ae3d0" "CP1" "Phiếu nhập chi phí mua hàng" "Purchase Cost Receipt" "Purchase Cost Entry" "mua_hang" 3 "1" "1" false
create_chung_tu "b8e7ee42-7974-5835-8167-e0de240f27ca" "DC5" "Phiếu nhập điều chỉnh giá hàng mua" "Purchase Price Adjustment Receipt" "Price Adjustment Receipt" "mua_hang" 4 "1" "1" false
create_chung_tu "43cb4826-c93e-5ea0-b5ea-60a1a288a342" "DH5" "Đơn hàng mua trong nước" "Domestic Purchase Order" "Purchase Order" "mua_hang" 5 "1" "1" true
create_chung_tu "4f59c6a0-dde0-5c36-b750-cd1a4d9f55bd" "DH6" "Đơn hàng mua nhập khẩu" "Import Purchase Order" "Import Order" "mua_hang" 6 "1" "1" true
create_chung_tu "d40a8879-acf6-5df6-bc7e-8d6d7185d49d" "HD4" "Hóa đơn mua hàng trong nước" "Domestic Purchase Invoice" "Purchase Invoice" "mua_hang" 7 "1" "1" true
create_chung_tu "990cd582-4805-5c16-a222-bafa0e8b28a6" "HD5" "Hóa đơn mua hàng nhập khẩu" "Import Purchase Invoice" "Import Invoice" "mua_hang" 8 "1" "1" true
create_chung_tu "c61ad6bc-93a9-5e2f-ac3d-a3f392668bf5" "HD6" "Hóa đơn mua dịch vụ" "Service Purchase Invoice" "Service Invoice" "mua_hang" 9 "0" "0" false
create_chung_tu "11e5067e-3aef-520e-acef-d713aeba84bb" "I30" "Hóa đơn đầu vào" "Input Invoice" "VAT Input Invoice" "mua_hang" 10 "1" "1" true
create_chung_tu "4883b3a1-5d71-504f-90dd-202b100fcf68" "TL5" "Phiếu xuất trả lại nhà cung cấp" "Supplier Return Issue Slip" "Return to Supplier" "mua_hang" 11 "1" "1" true
create_chung_tu "496faabf-f9e1-599a-8645-aea2511ac7cb" "TL6" "Hóa đơn dịch vụ trả lại nhà cung cấp" "Supplier Service Return Invoice" "Service Return to Supplier" "mua_hang" 12 "0" "0" false
create_chung_tu "7ba8085f-3916-5a7f-8154-615a60fbb7de" "TU1" "Phiếu thanh toán tạm ứng" "Advance Payment Voucher" "Advance Payment" "mua_hang" 13 "0" "0" false
create_chung_tu "4e884757-f5ff-5bbe-861d-9bab93dfcd9c" "HD7" "Hóa đơn nhập mua - xuất thẳng" "Direct Purchase-Sale Invoice" "Direct Sale Invoice" "mua_hang" 14 "1" "1" false
create_chung_tu "75647034-3ca9-5cf0-be7a-d1d6985df014" "PX4" "Phiếu xuất thẳng" "Direct Issue Slip" "Direct Issue" "mua_hang" 15 "1" "1" true
echo ""

# 6. TỒN KHO (xcode=ton_kho)
echo -e "${YELLOW}📁 6. TỒN KHO (xcode=ton_kho)${NC}"
create_chung_tu "53697c95-1da1-5cc6-8c69-f00d8c9e6e86" "KK1" "Phiếu yêu cầu kiểm kê" "Inventory Count Request" "Count Request" "ton_kho" 1 "1" "1" true
create_chung_tu "599e969f-e259-5d93-99aa-a72d70d2b77b" "PN1" "Phiếu nhập kho" "Goods Receipt" "Receipt" "ton_kho" 2 "1" "1" true
create_chung_tu "9c2571b3-85b5-53cd-ba38-19d85e4a0b96" "PN2" "Phiếu nhập điều chuyển" "Transfer Receipt" "Transfer In" "ton_kho" 3 "1" "1" true
create_chung_tu "4825a41d-7f34-5a31-8f19-a9beed8e5a0e" "PNX" "Phiếu nhập xuất thẳng" "Direct Receipt-Issue" "Direct Transfer" "ton_kho" 4 "1" "1" true
create_chung_tu "ac9123d8-3538-5c56-b531-46523af219b8" "PX1" "Phiếu xuất kho" "Goods Issue" "Issue" "ton_kho" 5 "1" "1" true
create_chung_tu "985c854b-9c36-5406-8a18-b99d1a9c337c" "PX2" "Phiếu xuất điều chuyển" "Transfer Issue" "Transfer Out" "ton_kho" 6 "1" "1" true
create_chung_tu "68357b30-19e0-5954-b8c3-d9ad5aaf9aee" "YC1" "Phiếu yêu cầu xuất kho" "Issue Request" "Request Issue" "ton_kho" 7 "1" "1" true
echo ""

# 7. GIÁ THÀNH (xcode=gia_thanh)
echo -e "${YELLOW}📁 7. GIÁ THÀNH (xcode=gia_thanh)${NC}"
create_chung_tu "6728eea4-1261-568d-931f-855be629e4c5" "DM1" "Định mức nguyên vật liệu" "Material Standard" "Material Norm" "gia_thanh" 1 "1" "1" false
create_chung_tu "4471ccf2-91d6-5220-a9e2-57928df3b7f8" "SX1" "Lệnh sản xuất" "Production Order" "Work Order" "gia_thanh" 2 "1" "1" true
echo ""

echo -e "${GREEN}🎉 Hoàn thành! Đã tạo tất cả 49 chứng từ cho hệ thống ERP.${NC}"
echo ""
echo -e "${BLUE}📊 Thống kê:${NC}"
echo -e "  • Tiền mặt: 3 chứng từ"
echo -e "  • Tiền gửi: 3 chứng từ"
echo -e "  • Bán hàng: 11 chứng từ"
echo -e "  • Hóa đơn: 8 chứng từ"
echo -e "  • Mua hàng: 15 chứng từ"
echo -e "  • Tồn kho: 7 chứng từ"
echo -e "  • Giá thành: 2 chứng từ"
echo -e "  ${GREEN}Tổng cộng: 49 chứng từ${NC}"
echo ""
echo -e "${YELLOW}Tiếp theo: Chạy script tạo quyển chứng từ và chi tiết quyển${NC}"
