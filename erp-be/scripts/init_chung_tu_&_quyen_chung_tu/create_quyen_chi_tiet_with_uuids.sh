#!/bin/bash

# Script tạo chi tiết quyển chứng từ với UUID hardcode
# Sử dụng UUID cố định thay vì mã chứng từ

set -e  # Exit on any error

# Màu sắc cho output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Cấu hình
SERVER="http://localhost:8000"
ENTITY_SLUG="tutimi-dnus2xnc"
USERNAME="tutimi"
PASSWORD="tutimi"

# Kiểm tra token
if [ -z "$TOKEN" ]; then
    echo -e "${YELLOW}Lấy token xác thực...${NC}"
    TOKEN_RESPONSE=$(curl -s -X POST "$SERVER/api/auth/token/" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")

    TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"token":"[^"]*' | cut -d'"' -f4)

    if [ -z "$TOKEN" ]; then
        echo -e "${RED}❌ Không thể lấy token. Kiểm tra username/password.${NC}"
        echo "Response: $TOKEN_RESPONSE"
        exit 1
    fi

    echo -e "${GREEN}✅ Đã lấy token thành công${NC}"
fi

# Function để thêm chi tiết vào quyển với UUID
add_chi_tiet_uuid() {
    local quyen_uuid=$1
    local chung_tu_uuid=$2
    local line=$3
    local ma_ct_name=$4

    echo -e "${BLUE}  Thêm chi tiết: $ma_ct_name (UUID: ${chung_tu_uuid:0:8}...)${NC}"

    RESPONSE=$(curl -s -X POST "$SERVER/api/entities/$ENTITY_SLUG/erp/document-books/$quyen_uuid/add-document-type/" \
        -H "Content-Type: application/json" \
        -H "Authorization: Token $TOKEN" \
        -d "{
            \"ma_ct\": \"$chung_tu_uuid\",
            \"line\": $line,
            \"username\": \"$USERNAME\"
        }")

    if echo "$RESPONSE" | grep -q "\"uuid\""; then
        echo -e "${GREEN}    ✅ Thành công: $ma_ct_name${NC}"
    else
        echo -e "${RED}    ❌ Lỗi: $ma_ct_name${NC}"
        echo "    Response: $RESPONSE"
    fi
}

echo -e "${YELLOW}🚀 Thêm chi tiết quyển chứng từ với UUID hardcode...${NC}"
echo ""

# UUID của các chứng từ (hardcode) - sử dụng variables thay vì associative arrays
DNC_UUID="013f2847-e3c3-502a-a6fd-4e85e75ce07d"
PC1_UUID="819c4a9d-6546-5f4b-bd93-989015a00d83"
PT1_UUID="b01da8f3-aeee-5f92-b9fd-5ea5438fc633"
BC1_UUID="88a13ee8-e801-59ac-9e08-14922b588f09"
BN1_UUID="81d417a5-9ac3-5534-b18f-17aef6b1f17c"
BG1_UUID="b35652a3-135d-5475-ba3f-f224336bce0d"
DH1_UUID="3c0b88fe-bcfb-53c7-8eab-20334b97d37f"
HD1_UUID="37d3e3fa-519a-5b9b-9f02-fedcf3950ed9"
DHO_UUID="e5cf682d-2def-595c-b04b-6654502e88a0"
HD2_UUID="8b40a8a5-9ff0-53f4-a99d-14fd0a0fdedb"
HD3_UUID="c2cacc44-abbb-57f6-b762-46d16d5bc26b"
PX3_UUID="1d69d473-1800-50da-8be3-acda17988d9b"
TL1_UUID="700c8a3c-ad6d-582d-85b9-753ef6f656e1"
TL2_UUID="ae932c88-0510-5dfe-9c6e-7282d0b31c38"
TL3_UUID="4a316dce-47c5-599c-8289-e71f2d72cff3"
TL4_UUID="5604568d-13af-50d5-9d5d-ffd00bb77196"
HD_GTGT_UUID="37df37d7-8518-55b3-acce-f4f28d501961"
HD_BH_UUID="cddaa5a1-4956-5aef-b8e9-df75b0f26d93"
HD_TM_UUID="2c7f5012-0c5c-537e-942f-3aef445d9860"
HD_DT_UUID="08c540fc-2e92-503d-908c-04dc6af6df5e"
HD_XK_UUID="702e9291-138f-5447-bb4e-1f8011d3d43d"
HD_NK_UUID="19d8871a-2888-5bde-a994-bfed2993da81"
HD_DV_UUID="0f8d698a-f3bd-51c7-a17c-21cc32ee5bf0"
HD_KH_UUID="ac117d08-1ec8-57f4-8910-34a9051d2372"
CN5_UUID="07252efa-958a-58a0-b84f-da9b4e25604a"
CN6_UUID="8a744cbb-304e-5bc2-a8b9-0d51bd89ce2d"
CP1_UUID="624f11cd-429c-53dd-b99e-f1efd24ae3d0"
DC5_UUID="b8e7ee42-**************-e0de240f27ca"
DH5_UUID="43cb4826-c93e-5ea0-b5ea-60a1a288a342"
DH6_UUID="4f59c6a0-dde0-5c36-b750-cd1a4d9f55bd"
HD4_UUID="d40a8879-acf6-5df6-bc7e-8d6d7185d49d"
HD5_UUID="990cd582-4805-5c16-a222-bafa0e8b28a6"
HD6_UUID="c61ad6bc-93a9-5e2f-ac3d-a3f392668bf5"
I30_UUID="11e5067e-3aef-520e-acef-d713aeba84bb"
TL5_UUID="4883b3a1-5d71-504f-90dd-202b100fcf68"
TL6_UUID="496faabf-f9e1-599a-8645-aea2511ac7cb"
TU1_UUID="7ba8085f-3916-5a7f-8154-615a60fbb7de"
HD7_UUID="4e884757-f5ff-5bbe-861d-9bab93dfcd9c"
PX4_UUID="75647034-3ca9-5cf0-be7a-d1d6985df014"
KK1_UUID="53697c95-1da1-5cc6-8c69-f00d8c9e6e86"
PN1_UUID="599e969f-e259-5d93-99aa-a72d70d2b77b"
PN2_UUID="9c2571b3-85b5-53cd-ba38-19d85e4a0b96"
PNX_UUID="4825a41d-7f34-5a31-8f19-a9beed8e5a0e"
PX1_UUID="ac9123d8-3538-5c56-b531-46523af219b8"
PX2_UUID="985c854b-9c36-5406-8a18-b99d1a9c337c"
YC1_UUID="68357b30-19e0-5954-b8c3-d9ad5aaf9aee"
DM1_UUID="6728eea4-1261-568d-931f-855be629e4c5"
SX1_UUID="4471ccf2-91d6-5220-a9e2-57928df3b7f8"

# UUID của các quyển chứng từ (hardcode)
QUYEN_DNC_UUID="61341a37-fa26-5642-bc07-1829f22204c2"
QUYEN_PC1_UUID="505b18d0-87e0-50c6-9196-7f0f838196b6"
QUYEN_PT1_UUID="a1ce1dc7-5be3-558c-b0f5-006aa6dfbbce"
QUYEN_BC1_UUID="33e401ed-c293-53b0-8e22-d5185c304987"
QUYEN_BN1_UUID="cbb249c3-54f2-5fcf-94e8-f93214a78c5a"
QUYEN_DNC_TG_UUID="ddfdd702-7b8f-53f5-bd63-9234c39ac92d"
QUYEN_BG1_UUID="1e8ceeb5-a0f8-5b3b-8e2d-f81c2a650154"
QUYEN_DH1_UUID="8b496141-408d-5c5e-ad1b-5224af93fad0"
QUYEN_HD1_UUID="fae8fa5d-1bd9-5600-a7f5-adcf4233500b"
QUYEN_DHO_UUID="fb83eeea-2736-5943-96d5-13ea7bdc4adc"
QUYEN_HD2_UUID="a033f1c9-2613-50f0-ad61-addf2e8332de"
QUYEN_HD3_UUID="846ea003-22ef-538d-88fd-27f41e75ba9b"
QUYEN_PX3_UUID="bf802aa1-ed16-5aa1-bb62-3786e411c920"
QUYEN_TL1_UUID="f2d2c6d8-7714-5c22-b6d5-5cadeee2d1cb"
QUYEN_TL2_UUID="91305a96-ba91-5773-bd64-24097f444087"
QUYEN_TL3_UUID="892cdabe-5cbe-5078-bf42-cf4aefad564b"
QUYEN_TL4_UUID="3ffdc290-8c70-5256-bc5b-1d650e4da99a"
QUYEN_HD_GTGT_UUID="d388e34b-9b19-56b1-94c9-60b3f8319ef9"
QUYEN_MH_DH_UUID="f56f69f6-ba75-550b-bd3a-46fb67924914"
QUYEN_TK_PN_UUID="7256281c-2ed4-5cb7-905b-8af16c78aefd"
QUYEN_GT_DM_UUID="64fa4b4d-a390-5c06-8cd2-9d408be02474"

# 1. TIỀN MẶT
echo -e "${YELLOW}📁 1. CHI TIẾT TIỀN MẶT${NC}"
add_chi_tiet_uuid "$QUYEN_DNC_UUID" "$DNC_UUID" 1 "DNC - Đề nghị thanh toán"
add_chi_tiet_uuid "$QUYEN_PC1_UUID" "$PC1_UUID" 1 "PC1 - Phiếu chi tiền"
add_chi_tiet_uuid "$QUYEN_PT1_UUID" "$PT1_UUID" 1 "PT1 - Phiếu thu tiền"
echo ""

# 2. TIỀN GỬI
echo -e "${YELLOW}📁 2. CHI TIẾT TIỀN GỬI${NC}"
add_chi_tiet_uuid "$QUYEN_BC1_UUID" "$BC1_UUID" 1 "BC1 - Giấy báo có 1"
add_chi_tiet_uuid "$QUYEN_BN1_UUID" "$BN1_UUID" 1 "BN1 - Giấy báo nợ"
add_chi_tiet_uuid "$QUYEN_DNC_TG_UUID" "$DNC_UUID" 1 "DNC - Đề nghị thanh toán"
echo ""

# 3. BÁN HÀNG
echo -e "${YELLOW}📁 3. CHI TIẾT BÁN HÀNG${NC}"
add_chi_tiet_uuid "$QUYEN_BG1_UUID" "$BG1_UUID" 1 "BG1 - Báo giá"
add_chi_tiet_uuid "$QUYEN_DH1_UUID" "$DH1_UUID" 1 "DH1 - Đơn hàng"
add_chi_tiet_uuid "$QUYEN_HD1_UUID" "$HD1_UUID" 1 "HD1 - Hóa đơn bán hàng"
add_chi_tiet_uuid "$QUYEN_DHO_UUID" "$DHO_UUID" 1 "DHO - Đơn hàng online"
add_chi_tiet_uuid "$QUYEN_HD2_UUID" "$HD2_UUID" 1 "HD2 - Hóa đơn bán lẻ"
add_chi_tiet_uuid "$QUYEN_HD3_UUID" "$HD3_UUID" 1 "HD3 - Hóa đơn xuất khẩu"
add_chi_tiet_uuid "$QUYEN_PX3_UUID" "$PX3_UUID" 1 "PX3 - Phiếu xuất bán hàng"
add_chi_tiet_uuid "$QUYEN_TL1_UUID" "$TL1_UUID" 1 "TL1 - Phiếu nhập trả lại"
add_chi_tiet_uuid "$QUYEN_TL2_UUID" "$TL2_UUID" 1 "TL2 - Hóa đơn trả lại"
add_chi_tiet_uuid "$QUYEN_TL3_UUID" "$TL3_UUID" 1 "TL3 - Hóa đơn giảm giá"
add_chi_tiet_uuid "$QUYEN_TL4_UUID" "$TL4_UUID" 1 "TL4 - Hóa đơn điều chỉnh tăng"
echo ""

# 4. HÓA ĐƠN (tập trung vào 1 quyển)
echo -e "${YELLOW}📁 4. CHI TIẾT HÓA ĐƠN${NC}"
add_chi_tiet_uuid "$QUYEN_HD_GTGT_UUID" "$HD_GTGT_UUID" 1 "HD_GTGT - Hóa đơn GTGT"
add_chi_tiet_uuid "$QUYEN_HD_GTGT_UUID" "$HD_BH_UUID" 2 "HD_BH - Hóa đơn bán hàng"
add_chi_tiet_uuid "$QUYEN_HD_GTGT_UUID" "$HD_TM_UUID" 3 "HD_TM - Hóa đơn thương mại"
add_chi_tiet_uuid "$QUYEN_HD_GTGT_UUID" "$HD_DT_UUID" 4 "HD_DT - Hóa đơn điện tử"
add_chi_tiet_uuid "$QUYEN_HD_GTGT_UUID" "$HD_XK_UUID" 5 "HD_XK - Hóa đơn xuất khẩu"
add_chi_tiet_uuid "$QUYEN_HD_GTGT_UUID" "$HD_NK_UUID" 6 "HD_NK - Hóa đơn nhập khẩu"
add_chi_tiet_uuid "$QUYEN_HD_GTGT_UUID" "$HD_DV_UUID" 7 "HD_DV - Hóa đơn dịch vụ"
add_chi_tiet_uuid "$QUYEN_HD_GTGT_UUID" "$HD_KH_UUID" 8 "HD_KH - Hóa đơn khác"
echo ""

# 5. MUA HÀNG (tập trung vào 1 quyển)
echo -e "${YELLOW}📁 5. CHI TIẾT MUA HÀNG${NC}"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$CN5_UUID" 1 "CN5 - Chứng từ phải trả khác"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$CN6_UUID" 2 "CN6 - Bút toán điều chỉnh giảm công nợ"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$CP1_UUID" 3 "CP1 - Phiếu nhập chi phí mua hàng"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$DC5_UUID" 4 "DC5 - Phiếu nhập điều chỉnh giá hàng mua"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$DH5_UUID" 5 "DH5 - Đơn hàng mua trong nước"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$DH6_UUID" 6 "DH6 - Đơn hàng mua nhập khẩu"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$HD4_UUID" 7 "HD4 - Hóa đơn mua hàng trong nước"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$HD5_UUID" 8 "HD5 - Hóa đơn mua hàng nhập khẩu"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$HD6_UUID" 9 "HD6 - Hóa đơn mua dịch vụ"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$I30_UUID" 10 "I30 - Hóa đơn đầu vào"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$TL5_UUID" 11 "TL5 - Phiếu xuất trả lại nhà cung cấp"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$TL6_UUID" 12 "TL6 - Hóa đơn dịch vụ trả lại nhà cung cấp"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$TU1_UUID" 13 "TU1 - Phiếu thanh toán tạm ứng"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$HD7_UUID" 14 "HD7 - Hóa đơn nhập mua - xuất thẳng"
add_chi_tiet_uuid "$QUYEN_MH_DH_UUID" "$PX4_UUID" 15 "PX4 - Phiếu xuất thẳng"
echo ""

# 6. TỒN KHO (tập trung vào 1 quyển)
echo -e "${YELLOW}📁 6. CHI TIẾT TỒN KHO${NC}"
add_chi_tiet_uuid "$QUYEN_TK_PN_UUID" "$KK1_UUID" 1 "KK1 - Phiếu yêu cầu kiểm kê"
add_chi_tiet_uuid "$QUYEN_TK_PN_UUID" "$PN1_UUID" 2 "PN1 - Phiếu nhập kho"
add_chi_tiet_uuid "$QUYEN_TK_PN_UUID" "$PN2_UUID" 3 "PN2 - Phiếu nhập điều chuyển"
add_chi_tiet_uuid "$QUYEN_TK_PN_UUID" "$PNX_UUID" 4 "PNX - Phiếu nhập xuất thẳng"
add_chi_tiet_uuid "$QUYEN_TK_PN_UUID" "$PX1_UUID" 5 "PX1 - Phiếu xuất kho"
add_chi_tiet_uuid "$QUYEN_TK_PN_UUID" "$PX2_UUID" 6 "PX2 - Phiếu xuất điều chuyển"
add_chi_tiet_uuid "$QUYEN_TK_PN_UUID" "$YC1_UUID" 7 "YC1 - Phiếu yêu cầu xuất kho"
echo ""

# 7. GIÁ THÀNH (tập trung vào 1 quyển)
echo -e "${YELLOW}📁 7. CHI TIẾT GIÁ THÀNH${NC}"
add_chi_tiet_uuid "$QUYEN_GT_DM_UUID" "$DM1_UUID" 1 "DM1 - Định mức nguyên vật liệu"
add_chi_tiet_uuid "$QUYEN_GT_DM_UUID" "$SX1_UUID" 2 "SX1 - Lệnh sản xuất"
echo ""

echo -e "${GREEN}🎉 Hoàn thành! Đã thêm tất cả chi tiết quyển với UUID hardcode.${NC}"
echo ""
echo -e "${BLUE}📊 Thống kê:${NC}"
echo -e "  • Tiền mặt: 3 chi tiết"
echo -e "  • Tiền gửi: 3 chi tiết"
echo -e "  • Bán hàng: 11 chi tiết"
echo -e "  • Hóa đơn: 8 chi tiết"
echo -e "  • Mua hàng: 15 chi tiết"
echo -e "  • Tồn kho: 7 chi tiết"
echo -e "  • Giá thành: 2 chi tiết"
echo -e "  ${GREEN}Tổng cộng: 49 chi tiết${NC}"
